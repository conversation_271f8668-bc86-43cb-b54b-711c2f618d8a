"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/document-locker/page",{

/***/ "(app-pages-browser)/./src/app/ui/document-locker/list.tsx":
/*!*********************************************!*\
  !*** ./src/app/ui/document-locker/list.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DocumentList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _app_lib_icons_SealogsDocumentLockerIcon__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/icons/SealogsDocumentLockerIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsDocumentLockerIcon.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _components_filter_components_document_locker_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/filter/components/document-locker-actions */ \"(app-pages-browser)/./src/components/filter/components/document-locker-actions.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DocumentList() {\n    _s();\n    const [vesselListWithDocuments, setVesselListWithDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [vesselListWithDocumentsCopy, setVesselListWithDocumentsCopy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryVesselListDocument] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.VESSEL_LIST_WITH_DOCUMENTS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: async (response)=>{\n            let filterData = [];\n            const data = response.readVessels.nodes;\n            if (data && data.length) {\n                for (const element of data){\n                    const documents = element.documents.nodes;\n                    for (const doc of documents){\n                        const modifiedDoc = {\n                            ...doc,\n                            type: element.__typename,\n                            type_title: element.title,\n                            type_id: element.id\n                        };\n                        filterData.push(modifiedDoc);\n                    }\n                }\n                setVesselListWithDocuments([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n                setVesselListWithDocumentsCopy([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventoriesEntry error\", error);\n        }\n    });\n    const [queryInventoriesListDocument] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GET_INVENTORIES_WITH_DOCUMENTS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: async (response)=>{\n            let filterData = [];\n            const data = response.readInventories.nodes;\n            if (data && data.length) {\n                for (const element of data){\n                    const documents = element.documents.nodes;\n                    for (const doc of documents){\n                        const modifiedDoc = {\n                            ...doc,\n                            type: element.__typename,\n                            type_title: element.title,\n                            type_id: element.id\n                        };\n                        filterData.push(modifiedDoc);\n                    }\n                }\n                setVesselListWithDocuments([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n                setVesselListWithDocumentsCopy([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventoriesEntry error\", error);\n        }\n    });\n    const [queryMaintenanceListDocument] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.MAINTENANCE_LIST_WITH_DOCUMENT, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: async (response)=>{\n            let filterData = [];\n            const data = response.readComponentMaintenanceChecks.nodes;\n            if (data && data.length) {\n                for (const element of data){\n                    const documents = element.documents.nodes;\n                    for (const doc of documents){\n                        const modifiedDoc = {\n                            ...doc,\n                            type: \"Maintenance\",\n                            type_title: element.name,\n                            type_id: element.id\n                        };\n                        filterData.push(modifiedDoc);\n                    }\n                }\n                setVesselListWithDocuments([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n                setVesselListWithDocumentsCopy([\n                    ...vesselListWithDocuments,\n                    ...filterData\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventoriesEntry error\", error);\n        }\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        let searchFilter = {\n            ...filter\n        };\n        let updatedVesselList = vesselListWithDocumentsCopy;\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.vesselID = {\n                    in: data.map((item)=>+item.value)\n                };\n                updatedVesselList = updatedVesselList.filter((item)=>searchFilter.vesselID.in.includes(+item.type_id));\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.vesselID = {\n                    eq: +data.value\n                };\n                updatedVesselList = updatedVesselList.filter((item)=>item.type_id === data.value.toString());\n            } else {\n                delete searchFilter.vesselID;\n            }\n        }\n        if (type === \"Module\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.moduleName = {\n                    in: data.map((item)=>item.value)\n                };\n                updatedVesselList = updatedVesselList.filter((item)=>searchFilter.moduleName.in.includes(item.type));\n            } else if (data && data.value && !Array.isArray(data)) {\n                searchFilter.moduleName = data.value;\n                updatedVesselList = updatedVesselList.filter((item)=>item.type === data.value);\n            } else {\n                delete searchFilter.moduleName;\n            }\n        }\n        if (type === \"keyword\") {\n            var _data_value;\n            if (data === null || data === void 0 ? void 0 : (_data_value = data.value) === null || _data_value === void 0 ? void 0 : _data_value.trim()) {\n                const lowerCaseContains = data.value.trim().toLowerCase();\n                searchFilter.item = {\n                    contains: lowerCaseContains\n                };\n                updatedVesselList = updatedVesselList.filter((item)=>{\n                    const lowerCaseTitle = item.title.toLowerCase();\n                    const lowerCaseName = item.name.toLowerCase();\n                    return lowerCaseTitle.includes(lowerCaseContains) || lowerCaseName.includes(lowerCaseContains);\n                });\n            } else {\n                delete searchFilter.item;\n            }\n        }\n        setFilter(searchFilter);\n        setVesselListWithDocuments(updatedVesselList);\n    };\n    const [readOneClient] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.READ_ONE_CLIENT, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneClient;\n            if (data) {\n                const docs = data.documents.nodes.map((doc)=>{\n                    return {\n                        ...doc,\n                        type: \"Company\",\n                        type_title: \"\",\n                        type_id: 0\n                    };\n                });\n                if (docs.length > 0) {\n                    setVesselListWithDocuments([\n                        ...vesselListWithDocuments,\n                        ...docs\n                    ]);\n                    setVesselListWithDocumentsCopy([\n                        ...vesselListWithDocuments,\n                        ...docs\n                    ]);\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readOneClient error\", error);\n        }\n    });\n    const loadClientDocuments = async ()=>{\n        var _localStorage_getItem;\n        await readOneClient({\n            variables: {\n                filter: {\n                    id: {\n                        eq: +((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0)\n                    }\n                }\n            }\n        });\n    };\n    const loadVesselDocuments = async ()=>{\n        await queryVesselListDocument();\n    };\n    const loadInventoryDocuments = async ()=>{\n        await queryInventoriesListDocument();\n    };\n    const loadMaintenanceDocuments = async ()=>{\n        await queryMaintenanceListDocument();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadVesselDocuments();\n            loadInventoryDocuments();\n            loadMaintenanceDocuments();\n            loadClientDocuments();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_6__.createColumns)([\n        {\n            accessorKey: \"name\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const document = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://api.sealogs.com/assets/\" + document.fileFilename,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"font-medium hover:underline flex items-center gap-1\",\n                                children: [\n                                    document.name,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 md:hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"text-xs\",\n                                    children: document.type\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 29\n                                }, this),\n                                document.type_title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: document.type_title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"block md:hidden text-sm text-muted-foreground\",\n                            children: [\n                                \"Created:\",\n                                \" \",\n                                (document === null || document === void 0 ? void 0 : document.created) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(document.created) : \"No Date\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.name) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.name) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"type\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Module\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const document = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                        type: \"normal\",\n                        variant: \"outline\",\n                        children: document.type\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.type) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.type) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"type_title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Item\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const document = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block text-sm\",\n                    children: !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(document.type_title) ? document.type_title : \"-\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.type_title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.type_title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"created\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Upload date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 17\n                }, this);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const document = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:block text-sm\",\n                    children: (document === null || document === void 0 ? void 0 : document.created) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_5__.formatDate)(document.created) : \"No Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 358,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.created) || 0).getTime();\n                const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.created) || 0).getTime();\n                return dateB - dateA;\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_8__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsDocumentLockerIcon__WEBPACK_IMPORTED_MODULE_9__.SealogsDocumentLockerIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 21\n                }, void 0),\n                title: \"Document locker\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_document_locker_actions__WEBPACK_IMPORTED_MODULE_11__.DocumentLockerFilterActions, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 26\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                lineNumber: 375,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_6__.DataTable, {\n                    columns: columns,\n                    data: vesselListWithDocuments,\n                    showToolbar: true,\n                    pageSize: 20,\n                    onChange: handleFilterOnChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\document-locker\\\\list.tsx\",\n                lineNumber: 382,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DocumentList, \"0Dd6wy81uhwY94XofMSLucfN7Jw=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery\n    ];\n});\n_c = DocumentList;\nvar _c;\n$RefreshReg$(_c, \"DocumentList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvZG9jdW1lbnQtbG9ja2VyL2xpc3QudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDa0Q7QUFFZjtBQUNVO0FBT2I7QUFDTTtBQUNlO0FBQ2dCO0FBQ0k7QUFDakI7QUFDNkI7QUFDOUM7QUFDSTtBQUN5RDtBQUVyRixTQUFTbUI7O0lBQ3BCLE1BQU0sQ0FBQ0MseUJBQXlCQywyQkFBMkIsR0FBR25CLCtDQUFRQSxDQUNsRSxFQUFFO0lBRU4sTUFBTSxDQUFDb0IsNkJBQTZCQywrQkFBK0IsR0FDL0RyQiwrQ0FBUUEsQ0FBQyxFQUFFO0lBRWYsTUFBTSxDQUFDc0IsUUFBUUMsVUFBVSxHQUFHdkIsK0NBQVFBLENBQUMsQ0FBQztJQUN0QyxNQUFNLENBQUN3QixXQUFXQyxhQUFhLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUUzQyxNQUFNLENBQUMwQix3QkFBd0IsR0FBR3hCLDZEQUFZQSxDQUFDQyw4RUFBMEJBLEVBQUU7UUFDdkV3QixhQUFhO1FBQ2JDLGFBQWEsT0FBT0M7WUFDaEIsSUFBSUMsYUFBYSxFQUFFO1lBQ25CLE1BQU1DLE9BQU9GLFNBQVNHLFdBQVcsQ0FBQ0MsS0FBSztZQUN2QyxJQUFJRixRQUFRQSxLQUFLRyxNQUFNLEVBQUU7Z0JBQ3JCLEtBQUssTUFBTUMsV0FBV0osS0FBTTtvQkFDeEIsTUFBTUssWUFBWUQsUUFBUUMsU0FBUyxDQUFDSCxLQUFLO29CQUN6QyxLQUFLLE1BQU1JLE9BQU9ELFVBQVc7d0JBQ3pCLE1BQU1FLGNBQWM7NEJBQ2hCLEdBQUdELEdBQUc7NEJBQ05FLE1BQU1KLFFBQVFLLFVBQVU7NEJBQ3hCQyxZQUFZTixRQUFRTyxLQUFLOzRCQUN6QkMsU0FBU1IsUUFBUVMsRUFBRTt3QkFDdkI7d0JBQ0FkLFdBQVdlLElBQUksQ0FBQ1A7b0JBQ3BCO2dCQUNKO2dCQUNBbkIsMkJBQTJCO3VCQUNwQkQ7dUJBQ0FZO2lCQUNOO2dCQUNEVCwrQkFBK0I7dUJBQ3hCSDt1QkFDQVk7aUJBQ047WUFDTDtRQUNKO1FBQ0FnQixTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQywrQkFBK0JBO1FBQ2pEO0lBQ0o7SUFFQSxNQUFNLENBQUNFLDZCQUE2QixHQUFHL0MsNkRBQVlBLENBQy9DRSxrRkFBOEJBLEVBQzlCO1FBQ0l1QixhQUFhO1FBQ2JDLGFBQWEsT0FBT0M7WUFDaEIsSUFBSUMsYUFBYSxFQUFFO1lBQ25CLE1BQU1DLE9BQU9GLFNBQVNxQixlQUFlLENBQUNqQixLQUFLO1lBQzNDLElBQUlGLFFBQVFBLEtBQUtHLE1BQU0sRUFBRTtnQkFDckIsS0FBSyxNQUFNQyxXQUFXSixLQUFNO29CQUN4QixNQUFNSyxZQUFZRCxRQUFRQyxTQUFTLENBQUNILEtBQUs7b0JBQ3pDLEtBQUssTUFBTUksT0FBT0QsVUFBVzt3QkFDekIsTUFBTUUsY0FBYzs0QkFDaEIsR0FBR0QsR0FBRzs0QkFDTkUsTUFBTUosUUFBUUssVUFBVTs0QkFDeEJDLFlBQVlOLFFBQVFPLEtBQUs7NEJBQ3pCQyxTQUFTUixRQUFRUyxFQUFFO3dCQUN2Qjt3QkFDQWQsV0FBV2UsSUFBSSxDQUFDUDtvQkFDcEI7Z0JBQ0o7Z0JBQ0FuQiwyQkFBMkI7dUJBQ3BCRDt1QkFDQVk7aUJBQ047Z0JBQ0RULCtCQUErQjt1QkFDeEJIO3VCQUNBWTtpQkFDTjtZQUNMO1FBQ0o7UUFDQWdCLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7UUFDakQ7SUFDSjtJQUdKLE1BQU0sQ0FBQ0ksNkJBQTZCLEdBQUdqRCw2REFBWUEsQ0FDL0NHLGtGQUE4QkEsRUFDOUI7UUFDSXNCLGFBQWE7UUFDYkMsYUFBYSxPQUFPQztZQUNoQixJQUFJQyxhQUFhLEVBQUU7WUFDbkIsTUFBTUMsT0FBT0YsU0FBU3VCLDhCQUE4QixDQUFDbkIsS0FBSztZQUMxRCxJQUFJRixRQUFRQSxLQUFLRyxNQUFNLEVBQUU7Z0JBQ3JCLEtBQUssTUFBTUMsV0FBV0osS0FBTTtvQkFDeEIsTUFBTUssWUFBWUQsUUFBUUMsU0FBUyxDQUFDSCxLQUFLO29CQUN6QyxLQUFLLE1BQU1JLE9BQU9ELFVBQVc7d0JBQ3pCLE1BQU1FLGNBQWM7NEJBQ2hCLEdBQUdELEdBQUc7NEJBQ05FLE1BQU07NEJBQ05FLFlBQVlOLFFBQVFrQixJQUFJOzRCQUN4QlYsU0FBU1IsUUFBUVMsRUFBRTt3QkFDdkI7d0JBQ0FkLFdBQVdlLElBQUksQ0FBQ1A7b0JBQ3BCO2dCQUNKO2dCQUNBbkIsMkJBQTJCO3VCQUNwQkQ7dUJBQ0FZO2lCQUNOO2dCQUNEVCwrQkFBK0I7dUJBQ3hCSDt1QkFDQVk7aUJBQ047WUFDTDtRQUNKO1FBQ0FnQixTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQywrQkFBK0JBO1FBQ2pEO0lBQ0o7SUFHSixNQUFNTyx1QkFBdUI7WUFBQyxFQUFFZixJQUFJLEVBQUVSLElBQUksRUFBTztRQUM3QyxJQUFJd0IsZUFBZTtZQUFFLEdBQUdqQyxNQUFNO1FBQUM7UUFDL0IsSUFBSWtDLG9CQUFvQnBDO1FBRXhCLElBQUltQixTQUFTLFVBQVU7WUFDbkIsSUFBSWtCLE1BQU1DLE9BQU8sQ0FBQzNCLFNBQVNBLEtBQUtHLE1BQU0sR0FBRyxHQUFHO2dCQUN4Q3FCLGFBQWFJLFFBQVEsR0FBRztvQkFBRUMsSUFBSTdCLEtBQUs4QixHQUFHLENBQUMsQ0FBQ0MsT0FBUyxDQUFDQSxLQUFLQyxLQUFLO2dCQUFFO2dCQUM5RFAsb0JBQW9CQSxrQkFBa0JsQyxNQUFNLENBQUMsQ0FBQ3dDLE9BQzFDUCxhQUFhSSxRQUFRLENBQUNDLEVBQUUsQ0FBQ0ksUUFBUSxDQUFDLENBQUNGLEtBQUtuQixPQUFPO1lBRXZELE9BQU8sSUFBSVosUUFBUSxDQUFDMEIsTUFBTUMsT0FBTyxDQUFDM0IsT0FBTztnQkFDckN3QixhQUFhSSxRQUFRLEdBQUc7b0JBQUVNLElBQUksQ0FBQ2xDLEtBQUtnQyxLQUFLO2dCQUFDO2dCQUMxQ1Asb0JBQW9CQSxrQkFBa0JsQyxNQUFNLENBQ3hDLENBQUN3QyxPQUFjQSxLQUFLbkIsT0FBTyxLQUFLWixLQUFLZ0MsS0FBSyxDQUFDRyxRQUFRO1lBRTNELE9BQU87Z0JBQ0gsT0FBT1gsYUFBYUksUUFBUTtZQUNoQztRQUNKO1FBRUEsSUFBSXBCLFNBQVMsVUFBVTtZQUNuQixJQUFJa0IsTUFBTUMsT0FBTyxDQUFDM0IsU0FBU0EsS0FBS0csTUFBTSxHQUFHLEdBQUc7Z0JBQ3hDcUIsYUFBYVksVUFBVSxHQUFHO29CQUN0QlAsSUFBSTdCLEtBQUs4QixHQUFHLENBQUMsQ0FBQ0MsT0FBY0EsS0FBS0MsS0FBSztnQkFDMUM7Z0JBQ0FQLG9CQUFvQkEsa0JBQWtCbEMsTUFBTSxDQUFDLENBQUN3QyxPQUMxQ1AsYUFBYVksVUFBVSxDQUFDUCxFQUFFLENBQUNJLFFBQVEsQ0FBQ0YsS0FBS3ZCLElBQUk7WUFFckQsT0FBTyxJQUFJUixRQUFRQSxLQUFLZ0MsS0FBSyxJQUFJLENBQUNOLE1BQU1DLE9BQU8sQ0FBQzNCLE9BQU87Z0JBQ25Ed0IsYUFBYVksVUFBVSxHQUFHcEMsS0FBS2dDLEtBQUs7Z0JBQ3BDUCxvQkFBb0JBLGtCQUFrQmxDLE1BQU0sQ0FDeEMsQ0FBQ3dDLE9BQWNBLEtBQUt2QixJQUFJLEtBQUtSLEtBQUtnQyxLQUFLO1lBRS9DLE9BQU87Z0JBQ0gsT0FBT1IsYUFBYVksVUFBVTtZQUNsQztRQUNKO1FBRUEsSUFBSTVCLFNBQVMsV0FBVztnQkFDaEJSO1lBQUosSUFBSUEsaUJBQUFBLDRCQUFBQSxjQUFBQSxLQUFNZ0MsS0FBSyxjQUFYaEMsa0NBQUFBLFlBQWFxQyxJQUFJLElBQUk7Z0JBQ3JCLE1BQU1DLG9CQUFvQnRDLEtBQUtnQyxLQUFLLENBQUNLLElBQUksR0FBR0UsV0FBVztnQkFDdkRmLGFBQWFPLElBQUksR0FBRztvQkFBRVMsVUFBVUY7Z0JBQWtCO2dCQUVsRGIsb0JBQW9CQSxrQkFBa0JsQyxNQUFNLENBQUMsQ0FBQ3dDO29CQUMxQyxNQUFNVSxpQkFBaUJWLEtBQUtwQixLQUFLLENBQUM0QixXQUFXO29CQUM3QyxNQUFNRyxnQkFBZ0JYLEtBQUtULElBQUksQ0FBQ2lCLFdBQVc7b0JBQzNDLE9BQ0lFLGVBQWVSLFFBQVEsQ0FBQ0ssc0JBQ3hCSSxjQUFjVCxRQUFRLENBQUNLO2dCQUUvQjtZQUNKLE9BQU87Z0JBQ0gsT0FBT2QsYUFBYU8sSUFBSTtZQUM1QjtRQUNKO1FBRUF2QyxVQUFVZ0M7UUFDVnBDLDJCQUEyQnFDO0lBQy9CO0lBRUEsTUFBTSxDQUFDa0IsY0FBYyxHQUFHeEUsNkRBQVlBLENBQUNJLG1FQUFlQSxFQUFFO1FBQ2xEcUIsYUFBYTtRQUNiQyxhQUFhLENBQUNDO1lBQ1YsTUFBTUUsT0FBT0YsU0FBUzZDLGFBQWE7WUFDbkMsSUFBSTNDLE1BQU07Z0JBQ04sTUFBTTRDLE9BQU81QyxLQUFLSyxTQUFTLENBQUNILEtBQUssQ0FBQzRCLEdBQUcsQ0FBQyxDQUFDeEI7b0JBQ25DLE9BQU87d0JBQ0gsR0FBR0EsR0FBRzt3QkFDTkUsTUFBTTt3QkFDTkUsWUFBWTt3QkFDWkUsU0FBUztvQkFDYjtnQkFDSjtnQkFDQSxJQUFJZ0MsS0FBS3pDLE1BQU0sR0FBRyxHQUFHO29CQUNqQmYsMkJBQTJCOzJCQUNwQkQ7MkJBQ0F5RDtxQkFDTjtvQkFDRHRELCtCQUErQjsyQkFDeEJIOzJCQUNBeUQ7cUJBQ047Z0JBQ0w7WUFDSjtRQUNKO1FBQ0E3QixTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQyx1QkFBdUJBO1FBQ3pDO0lBQ0o7SUFDQSxNQUFNNkIsc0JBQXNCO1lBSUFDO1FBSHhCLE1BQU1ILGNBQWM7WUFDaEJJLFdBQVc7Z0JBQ1B4RCxRQUFRO29CQUNKc0IsSUFBSTt3QkFBRXFCLElBQUksQ0FBRVksQ0FBQUEsQ0FBQUEsd0JBQUFBLGFBQWFFLE9BQU8sQ0FBQyx5QkFBckJGLG1DQUFBQSx3QkFBb0M7b0JBQUc7Z0JBQ3ZEO1lBQ0o7UUFDSjtJQUNKO0lBRUEsTUFBTUcsc0JBQXNCO1FBQ3hCLE1BQU10RDtJQUNWO0lBQ0EsTUFBTXVELHlCQUF5QjtRQUMzQixNQUFNaEM7SUFDVjtJQUNBLE1BQU1pQywyQkFBMkI7UUFDN0IsTUFBTS9CO0lBQ1Y7SUFDQXBELGdEQUFTQSxDQUFDO1FBQ04sSUFBSXlCLFdBQVc7WUFDWHdEO1lBQ0FDO1lBQ0FDO1lBQ0FOO1lBQ0FuRCxhQUFhO1FBQ2pCO0lBQ0osR0FBRztRQUFDRDtLQUFVO0lBRWQsTUFBTTJELFVBQVV6RSx3RUFBYUEsQ0FBQztRQUMxQjtZQUNJMEUsYUFBYTtZQUNiQyxRQUFRO29CQUFDLEVBQUVDLE1BQU0sRUFBbUI7cUNBQ2hDLDhEQUFDM0UsbUZBQW1CQTtvQkFBQzJFLFFBQVFBO29CQUFRNUMsT0FBTTs7Ozs7OztZQUUvQzZDLGVBQWU7WUFDZkMsTUFBTTtvQkFBQyxFQUFFQyxHQUFHLEVBQWdCO2dCQUN4QixNQUFNQyxXQUFnQkQsSUFBSUUsUUFBUTtnQkFDbEMscUJBQ0ksOERBQUNDO29CQUFJQyxXQUFVOztzQ0FDWCw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ1gsNEVBQUNDO2dDQUNHQyxNQUNJQyxpQ0FBeUIsR0FDekJOLFNBQVNTLFlBQVk7Z0NBRXpCQyxRQUFPO2dDQUNQQyxLQUFJO2dDQUNKUixXQUFVOztvQ0FDVEgsU0FBU3JDLElBQUk7a0RBQ2QsOERBQUN0Qyx5RkFBWUE7d0NBQUM4RSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHaEMsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDWCw4REFBQy9FLGtEQUFLQTtvQ0FBQ3dGLFNBQVE7b0NBQVVULFdBQVU7OENBQzlCSCxTQUFTbkQsSUFBSTs7Ozs7O2dDQUVqQm1ELFNBQVNqRCxVQUFVLGtCQUNoQiw4REFBQzhEO29DQUFLVixXQUFVOzhDQUNYSCxTQUFTakQsVUFBVTs7Ozs7Ozs7Ozs7O3NDQUloQyw4REFBQ21EOzRCQUFJQyxXQUFVOztnQ0FBZ0Q7Z0NBQ2xEO2dDQUNSSCxDQUFBQSxxQkFBQUEsK0JBQUFBLFNBQVVjLE9BQU8sSUFDWmhHLG1FQUFVQSxDQUFDa0YsU0FBU2MsT0FBTyxJQUMzQjs7Ozs7Ozs7Ozs7OztZQUl0QjtZQUNBQyxXQUFXLENBQUNDLE1BQVdDO29CQUNKRCxnQkFDQUM7Z0JBRGYsTUFBTUMsU0FBU0YsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTWYsUUFBUSxjQUFkZSxxQ0FBQUEsZUFBZ0JyRCxJQUFJLEtBQUk7Z0JBQ3ZDLE1BQU13RCxTQUFTRixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNaEIsUUFBUSxjQUFkZ0IscUNBQUFBLGVBQWdCdEQsSUFBSSxLQUFJO2dCQUN2QyxPQUFPdUQsT0FBT0UsYUFBYSxDQUFDRDtZQUNoQztRQUNKO1FBQ0E7WUFDSXpCLGFBQWE7WUFDYkMsUUFBUTtvQkFBQyxFQUFFQyxNQUFNLEVBQW1CO3FDQUNoQyw4REFBQzNFLG1GQUFtQkE7b0JBQUMyRSxRQUFRQTtvQkFBUTVDLE9BQU07Ozs7Ozs7WUFFL0M2QyxlQUFlO1lBQ2ZDLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtnQkFDeEIsTUFBTUMsV0FBV0QsSUFBSUUsUUFBUTtnQkFDN0IscUJBQ0ksOERBQUNDO29CQUFJQyxXQUFVOzhCQUNYLDRFQUFDL0Usa0RBQUtBO3dCQUFDeUIsTUFBSzt3QkFBUytELFNBQVE7a0NBQ3hCWixTQUFTbkQsSUFBSTs7Ozs7Ozs7Ozs7WUFJOUI7WUFDQWtFLFdBQVcsQ0FBQ0MsTUFBV0M7b0JBQ0pELGdCQUNBQztnQkFEZixNQUFNQyxTQUFTRixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNZixRQUFRLGNBQWRlLHFDQUFBQSxlQUFnQm5FLElBQUksS0FBSTtnQkFDdkMsTUFBTXNFLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1oQixRQUFRLGNBQWRnQixxQ0FBQUEsZUFBZ0JwRSxJQUFJLEtBQUk7Z0JBQ3ZDLE9BQU9xRSxPQUFPRSxhQUFhLENBQUNEO1lBQ2hDO1FBQ0o7UUFDQTtZQUNJekIsYUFBYTtZQUNiQyxRQUFRO29CQUFDLEVBQUVDLE1BQU0sRUFBbUI7cUNBQ2hDLDhEQUFDM0UsbUZBQW1CQTtvQkFBQzJFLFFBQVFBO29CQUFRNUMsT0FBTTs7Ozs7OztZQUcvQzhDLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtnQkFDeEIsTUFBTUMsV0FBV0QsSUFBSUUsUUFBUTtnQkFDN0IscUJBQ0ksOERBQUNDO29CQUFJQyxXQUFVOzhCQUNWLENBQUN0RixxREFBT0EsQ0FBQ21GLFNBQVNqRCxVQUFVLElBQ3ZCaUQsU0FBU2pELFVBQVUsR0FDbkI7Ozs7OztZQUdsQjtZQUNBZ0UsV0FBVyxDQUFDQyxNQUFXQztvQkFDSkQsZ0JBQ0FDO2dCQURmLE1BQU1DLFNBQVNGLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1mLFFBQVEsY0FBZGUscUNBQUFBLGVBQWdCakUsVUFBVSxLQUFJO2dCQUM3QyxNQUFNb0UsU0FBU0YsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTWhCLFFBQVEsY0FBZGdCLHFDQUFBQSxlQUFnQmxFLFVBQVUsS0FBSTtnQkFDN0MsT0FBT21FLE9BQU9FLGFBQWEsQ0FBQ0Q7WUFDaEM7UUFDSjtRQUNBO1lBQ0l6QixhQUFhO1lBQ2JDLFFBQVE7b0JBQUMsRUFBRUMsTUFBTSxFQUFtQjtxQ0FDaEMsOERBQUMzRSxtRkFBbUJBO29CQUFDMkUsUUFBUUE7b0JBQVE1QyxPQUFNOzs7Ozs7O1lBRS9DNkMsZUFBZTtZQUNmQyxNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7Z0JBQ3hCLE1BQU1DLFdBQVdELElBQUlFLFFBQVE7Z0JBQzdCLHFCQUNJLDhEQUFDQztvQkFBSUMsV0FBVTs4QkFDVkgsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVYyxPQUFPLElBQ1poRyxtRUFBVUEsQ0FBQ2tGLFNBQVNjLE9BQU8sSUFDM0I7Ozs7OztZQUdsQjtZQUNBQyxXQUFXLENBQUNDLE1BQVdDO29CQUNJRCxnQkFDQUM7Z0JBRHZCLE1BQU1JLFFBQVEsSUFBSUMsS0FBS04sQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTWYsUUFBUSxjQUFkZSxxQ0FBQUEsZUFBZ0JGLE9BQU8sS0FBSSxHQUFHUyxPQUFPO2dCQUM1RCxNQUFNQyxRQUFRLElBQUlGLEtBQUtMLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1oQixRQUFRLGNBQWRnQixxQ0FBQUEsZUFBZ0JILE9BQU8sS0FBSSxHQUFHUyxPQUFPO2dCQUM1RCxPQUFPQyxRQUFRSDtZQUNuQjtRQUNKO0tBQ0g7SUFFRCxxQkFDSTs7MEJBQ0ksOERBQUNuRyxrRUFBVUE7Z0JBQ1B1RyxvQkFDSSw4REFBQ3RHLCtGQUF5QkE7b0JBQUNnRixXQUFVOzs7Ozs7Z0JBRXpDbkQsT0FBTTtnQkFDTjBFLHVCQUFTLDhEQUFDcEcsK0dBQTJCQTs7Ozs7Ozs7OzswQkFFekMsOERBQUM0RTtnQkFBSUMsV0FBVTswQkFDVnJFLDBCQUNHLDhEQUFDdkIsb0RBQU9BOzs7O3lDQUVSLDhEQUFDUSxnRUFBU0E7b0JBQ04wRSxTQUFTQTtvQkFDVHBELE1BQU1iO29CQUNObUcsYUFBYTtvQkFDYkMsVUFBVTtvQkFDVkMsVUFBVWpFOzs7Ozs7Ozs7Ozs7O0FBTWxDO0dBdFh3QnJDOztRQVVjZix5REFBWUE7UUFpQ1BBLHlEQUFZQTtRQW9DWkEseURBQVlBO1FBZ0czQkEseURBQVlBOzs7S0EvS2hCZSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3VpL2RvY3VtZW50LWxvY2tlci9saXN0LnRzeD81ZjA3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xyXG5cclxuaW1wb3J0IExvYWRpbmcgZnJvbSAnQC9hcHAvbG9hZGluZydcclxuaW1wb3J0IHsgdXNlTGF6eVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnXHJcblxyXG5pbXBvcnQge1xyXG4gICAgVkVTU0VMX0xJU1RfV0lUSF9ET0NVTUVOVFMsXHJcbiAgICBHRVRfSU5WRU5UT1JJRVNfV0lUSF9ET0NVTUVOVFMsXHJcbiAgICBNQUlOVEVOQU5DRV9MSVNUX1dJVEhfRE9DVU1FTlQsXHJcbiAgICBSRUFEX09ORV9DTElFTlQsXHJcbn0gZnJvbSAnQC9hcHAvbGliL2dyYXBoUUwvcXVlcnknXHJcbmltcG9ydCB7IGlzRW1wdHksIHRyaW0gfSBmcm9tICdsb2Rhc2gnXHJcbmltcG9ydCB7IGZvcm1hdERhdGUgfSBmcm9tICdAL2FwcC9oZWxwZXJzL2RhdGVIZWxwZXInXHJcbmltcG9ydCB7IERhdGFUYWJsZSwgY3JlYXRlQ29sdW1ucyB9IGZyb20gJ0AvY29tcG9uZW50cy9maWx0ZXJlZFRhYmxlJ1xyXG5pbXBvcnQgeyBEYXRhVGFibGVTb3J0SGVhZGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL2RhdGEtdGFibGUtc29ydC1oZWFkZXInXHJcbmltcG9ydCB7IExpc3RIZWFkZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvbGlzdC1oZWFkZXInXHJcbmltcG9ydCB7IFNlYWxvZ3NEb2N1bWVudExvY2tlckljb24gfSBmcm9tICdAL2FwcC9saWIvaWNvbnMvU2VhbG9nc0RvY3VtZW50TG9ja2VySWNvbidcclxuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWknXHJcbmltcG9ydCB7IEV4dGVybmFsTGluayB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcclxuaW1wb3J0IHsgRG9jdW1lbnRMb2NrZXJGaWx0ZXJBY3Rpb25zIH0gZnJvbSAnQC9jb21wb25lbnRzL2ZpbHRlci9jb21wb25lbnRzL2RvY3VtZW50LWxvY2tlci1hY3Rpb25zJ1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnRMaXN0KCkge1xyXG4gICAgY29uc3QgW3Zlc3NlbExpc3RXaXRoRG9jdW1lbnRzLCBzZXRWZXNzZWxMaXN0V2l0aERvY3VtZW50c10gPSB1c2VTdGF0ZShcclxuICAgICAgICBbXSBhcyBhbnksXHJcbiAgICApXHJcbiAgICBjb25zdCBbdmVzc2VsTGlzdFdpdGhEb2N1bWVudHNDb3B5LCBzZXRWZXNzZWxMaXN0V2l0aERvY3VtZW50c0NvcHldID1cclxuICAgICAgICB1c2VTdGF0ZShbXSBhcyBhbnkpXHJcblxyXG4gICAgY29uc3QgW2ZpbHRlciwgc2V0RmlsdGVyXSA9IHVzZVN0YXRlKHt9IGFzIFNlYXJjaEZpbHRlcilcclxuICAgIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxyXG5cclxuICAgIGNvbnN0IFtxdWVyeVZlc3NlbExpc3REb2N1bWVudF0gPSB1c2VMYXp5UXVlcnkoVkVTU0VMX0xJU1RfV0lUSF9ET0NVTUVOVFMsIHtcclxuICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcclxuICAgICAgICBvbkNvbXBsZXRlZDogYXN5bmMgKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgbGV0IGZpbHRlckRhdGEgPSBbXVxyXG4gICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UucmVhZFZlc3NlbHMubm9kZXNcclxuICAgICAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5sZW5ndGgpIHtcclxuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgZWxlbWVudCBvZiBkYXRhKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZG9jdW1lbnRzID0gZWxlbWVudC5kb2N1bWVudHMubm9kZXNcclxuICAgICAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGRvYyBvZiBkb2N1bWVudHMpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbW9kaWZpZWREb2MgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5kb2MsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiBlbGVtZW50Ll9fdHlwZW5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlX3RpdGxlOiBlbGVtZW50LnRpdGxlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZV9pZDogZWxlbWVudC5pZCwgLy8gQXNzdW1pbmcgYGlkYCBpcyB0aGUgdW5pcXVlIGlkZW50aWZpZXIgZm9yIHRoZSB2ZXNzZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBmaWx0ZXJEYXRhLnB1c2gobW9kaWZpZWREb2MpXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgc2V0VmVzc2VsTGlzdFdpdGhEb2N1bWVudHMoW1xyXG4gICAgICAgICAgICAgICAgICAgIC4uLnZlc3NlbExpc3RXaXRoRG9jdW1lbnRzLFxyXG4gICAgICAgICAgICAgICAgICAgIC4uLmZpbHRlckRhdGEsXHJcbiAgICAgICAgICAgICAgICBdKVxyXG4gICAgICAgICAgICAgICAgc2V0VmVzc2VsTGlzdFdpdGhEb2N1bWVudHNDb3B5KFtcclxuICAgICAgICAgICAgICAgICAgICAuLi52ZXNzZWxMaXN0V2l0aERvY3VtZW50cyxcclxuICAgICAgICAgICAgICAgICAgICAuLi5maWx0ZXJEYXRhLFxyXG4gICAgICAgICAgICAgICAgXSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcigncXVlcnlJbnZlbnRvcmllc0VudHJ5IGVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgfSxcclxuICAgIH0pXHJcblxyXG4gICAgY29uc3QgW3F1ZXJ5SW52ZW50b3JpZXNMaXN0RG9jdW1lbnRdID0gdXNlTGF6eVF1ZXJ5KFxyXG4gICAgICAgIEdFVF9JTlZFTlRPUklFU19XSVRIX0RPQ1VNRU5UUyxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgICAgICBvbkNvbXBsZXRlZDogYXN5bmMgKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGxldCBmaWx0ZXJEYXRhID0gW11cclxuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5yZWFkSW52ZW50b3JpZXMubm9kZXNcclxuICAgICAgICAgICAgICAgIGlmIChkYXRhICYmIGRhdGEubGVuZ3RoKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBlbGVtZW50IG9mIGRhdGEpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZG9jdW1lbnRzID0gZWxlbWVudC5kb2N1bWVudHMubm9kZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBkb2Mgb2YgZG9jdW1lbnRzKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtb2RpZmllZERvYyA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5kb2MsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogZWxlbWVudC5fX3R5cGVuYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGVfdGl0bGU6IGVsZW1lbnQudGl0bGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZV9pZDogZWxlbWVudC5pZCwgLy8gQXNzdW1pbmcgYGlkYCBpcyB0aGUgdW5pcXVlIGlkZW50aWZpZXIgZm9yIHRoZSB2ZXNzZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlckRhdGEucHVzaChtb2RpZmllZERvYylcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBzZXRWZXNzZWxMaXN0V2l0aERvY3VtZW50cyhbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnZlc3NlbExpc3RXaXRoRG9jdW1lbnRzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5maWx0ZXJEYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgIF0pXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VmVzc2VsTGlzdFdpdGhEb2N1bWVudHNDb3B5KFtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4udmVzc2VsTGlzdFdpdGhEb2N1bWVudHMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLmZpbHRlckRhdGEsXHJcbiAgICAgICAgICAgICAgICAgICAgXSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3F1ZXJ5SW52ZW50b3JpZXNFbnRyeSBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICApXHJcblxyXG4gICAgY29uc3QgW3F1ZXJ5TWFpbnRlbmFuY2VMaXN0RG9jdW1lbnRdID0gdXNlTGF6eVF1ZXJ5KFxyXG4gICAgICAgIE1BSU5URU5BTkNFX0xJU1RfV0lUSF9ET0NVTUVOVCxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgICAgICBvbkNvbXBsZXRlZDogYXN5bmMgKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGxldCBmaWx0ZXJEYXRhID0gW11cclxuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5yZWFkQ29tcG9uZW50TWFpbnRlbmFuY2VDaGVja3Mubm9kZXNcclxuICAgICAgICAgICAgICAgIGlmIChkYXRhICYmIGRhdGEubGVuZ3RoKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBlbGVtZW50IG9mIGRhdGEpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZG9jdW1lbnRzID0gZWxlbWVudC5kb2N1bWVudHMubm9kZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBkb2Mgb2YgZG9jdW1lbnRzKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtb2RpZmllZERvYyA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5kb2MsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ01haW50ZW5hbmNlJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlX3RpdGxlOiBlbGVtZW50Lm5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZV9pZDogZWxlbWVudC5pZCwgLy8gQXNzdW1pbmcgYGlkYCBpcyB0aGUgdW5pcXVlIGlkZW50aWZpZXIgZm9yIHRoZSB2ZXNzZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlckRhdGEucHVzaChtb2RpZmllZERvYylcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBzZXRWZXNzZWxMaXN0V2l0aERvY3VtZW50cyhbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnZlc3NlbExpc3RXaXRoRG9jdW1lbnRzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5maWx0ZXJEYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgIF0pXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VmVzc2VsTGlzdFdpdGhEb2N1bWVudHNDb3B5KFtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4udmVzc2VsTGlzdFdpdGhEb2N1bWVudHMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLmZpbHRlckRhdGEsXHJcbiAgICAgICAgICAgICAgICAgICAgXSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3F1ZXJ5SW52ZW50b3JpZXNFbnRyeSBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICApXHJcblxyXG4gICAgY29uc3QgaGFuZGxlRmlsdGVyT25DaGFuZ2UgPSAoeyB0eXBlLCBkYXRhIH06IGFueSkgPT4ge1xyXG4gICAgICAgIGxldCBzZWFyY2hGaWx0ZXIgPSB7IC4uLmZpbHRlciB9XHJcbiAgICAgICAgbGV0IHVwZGF0ZWRWZXNzZWxMaXN0ID0gdmVzc2VsTGlzdFdpdGhEb2N1bWVudHNDb3B5XHJcblxyXG4gICAgICAgIGlmICh0eXBlID09PSAndmVzc2VsJykge1xyXG4gICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShkYXRhKSAmJiBkYXRhLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgICAgIHNlYXJjaEZpbHRlci52ZXNzZWxJRCA9IHsgaW46IGRhdGEubWFwKChpdGVtKSA9PiAraXRlbS52YWx1ZSkgfVxyXG4gICAgICAgICAgICAgICAgdXBkYXRlZFZlc3NlbExpc3QgPSB1cGRhdGVkVmVzc2VsTGlzdC5maWx0ZXIoKGl0ZW06IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICBzZWFyY2hGaWx0ZXIudmVzc2VsSUQuaW4uaW5jbHVkZXMoK2l0ZW0udHlwZV9pZCksXHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoZGF0YSAmJiAhQXJyYXkuaXNBcnJheShkYXRhKSkge1xyXG4gICAgICAgICAgICAgICAgc2VhcmNoRmlsdGVyLnZlc3NlbElEID0geyBlcTogK2RhdGEudmFsdWUgfVxyXG4gICAgICAgICAgICAgICAgdXBkYXRlZFZlc3NlbExpc3QgPSB1cGRhdGVkVmVzc2VsTGlzdC5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgKGl0ZW06IGFueSkgPT4gaXRlbS50eXBlX2lkID09PSBkYXRhLnZhbHVlLnRvU3RyaW5nKCksXHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBkZWxldGUgc2VhcmNoRmlsdGVyLnZlc3NlbElEXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmICh0eXBlID09PSAnTW9kdWxlJykge1xyXG4gICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShkYXRhKSAmJiBkYXRhLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgICAgIHNlYXJjaEZpbHRlci5tb2R1bGVOYW1lID0ge1xyXG4gICAgICAgICAgICAgICAgICAgIGluOiBkYXRhLm1hcCgoaXRlbTogYW55KSA9PiBpdGVtLnZhbHVlKSxcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIHVwZGF0ZWRWZXNzZWxMaXN0ID0gdXBkYXRlZFZlc3NlbExpc3QuZmlsdGVyKChpdGVtOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgc2VhcmNoRmlsdGVyLm1vZHVsZU5hbWUuaW4uaW5jbHVkZXMoaXRlbS50eXBlKSxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChkYXRhICYmIGRhdGEudmFsdWUgJiYgIUFycmF5LmlzQXJyYXkoZGF0YSkpIHtcclxuICAgICAgICAgICAgICAgIHNlYXJjaEZpbHRlci5tb2R1bGVOYW1lID0gZGF0YS52YWx1ZVxyXG4gICAgICAgICAgICAgICAgdXBkYXRlZFZlc3NlbExpc3QgPSB1cGRhdGVkVmVzc2VsTGlzdC5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgKGl0ZW06IGFueSkgPT4gaXRlbS50eXBlID09PSBkYXRhLnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgZGVsZXRlIHNlYXJjaEZpbHRlci5tb2R1bGVOYW1lXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmICh0eXBlID09PSAna2V5d29yZCcpIHtcclxuICAgICAgICAgICAgaWYgKGRhdGE/LnZhbHVlPy50cmltKCkpIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGxvd2VyQ2FzZUNvbnRhaW5zID0gZGF0YS52YWx1ZS50cmltKCkudG9Mb3dlckNhc2UoKVxyXG4gICAgICAgICAgICAgICAgc2VhcmNoRmlsdGVyLml0ZW0gPSB7IGNvbnRhaW5zOiBsb3dlckNhc2VDb250YWlucyB9XHJcblxyXG4gICAgICAgICAgICAgICAgdXBkYXRlZFZlc3NlbExpc3QgPSB1cGRhdGVkVmVzc2VsTGlzdC5maWx0ZXIoKGl0ZW06IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGxvd2VyQ2FzZVRpdGxlID0gaXRlbS50aXRsZS50b0xvd2VyQ2FzZSgpXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbG93ZXJDYXNlTmFtZSA9IGl0ZW0ubmFtZS50b0xvd2VyQ2FzZSgpXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgbG93ZXJDYXNlVGl0bGUuaW5jbHVkZXMobG93ZXJDYXNlQ29udGFpbnMpIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxvd2VyQ2FzZU5hbWUuaW5jbHVkZXMobG93ZXJDYXNlQ29udGFpbnMpXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGRlbGV0ZSBzZWFyY2hGaWx0ZXIuaXRlbVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBzZXRGaWx0ZXIoc2VhcmNoRmlsdGVyKVxyXG4gICAgICAgIHNldFZlc3NlbExpc3RXaXRoRG9jdW1lbnRzKHVwZGF0ZWRWZXNzZWxMaXN0KVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IFtyZWFkT25lQ2xpZW50XSA9IHVzZUxhenlRdWVyeShSRUFEX09ORV9DTElFTlQsIHtcclxuICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcclxuICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLnJlYWRPbmVDbGllbnRcclxuICAgICAgICAgICAgaWYgKGRhdGEpIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGRvY3MgPSBkYXRhLmRvY3VtZW50cy5ub2Rlcy5tYXAoKGRvYzogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4uZG9jLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAnQ29tcGFueScsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGVfdGl0bGU6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlX2lkOiAwLFxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICBpZiAoZG9jcy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VmVzc2VsTGlzdFdpdGhEb2N1bWVudHMoW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi52ZXNzZWxMaXN0V2l0aERvY3VtZW50cyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4uZG9jcyxcclxuICAgICAgICAgICAgICAgICAgICBdKVxyXG4gICAgICAgICAgICAgICAgICAgIHNldFZlc3NlbExpc3RXaXRoRG9jdW1lbnRzQ29weShbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnZlc3NlbExpc3RXaXRoRG9jdW1lbnRzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5kb2NzLFxyXG4gICAgICAgICAgICAgICAgICAgIF0pXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3JlYWRPbmVDbGllbnQgZXJyb3InLCBlcnJvcilcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuICAgIGNvbnN0IGxvYWRDbGllbnREb2N1bWVudHMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgYXdhaXQgcmVhZE9uZUNsaWVudCh7XHJcbiAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgZmlsdGVyOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWQ6IHsgZXE6ICsobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2NsaWVudElkJykgPz8gMCkgfSxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBsb2FkVmVzc2VsRG9jdW1lbnRzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgIGF3YWl0IHF1ZXJ5VmVzc2VsTGlzdERvY3VtZW50KClcclxuICAgIH1cclxuICAgIGNvbnN0IGxvYWRJbnZlbnRvcnlEb2N1bWVudHMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgYXdhaXQgcXVlcnlJbnZlbnRvcmllc0xpc3REb2N1bWVudCgpXHJcbiAgICB9XHJcbiAgICBjb25zdCBsb2FkTWFpbnRlbmFuY2VEb2N1bWVudHMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgYXdhaXQgcXVlcnlNYWludGVuYW5jZUxpc3REb2N1bWVudCgpXHJcbiAgICB9XHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmIChpc0xvYWRpbmcpIHtcclxuICAgICAgICAgICAgbG9hZFZlc3NlbERvY3VtZW50cygpXHJcbiAgICAgICAgICAgIGxvYWRJbnZlbnRvcnlEb2N1bWVudHMoKVxyXG4gICAgICAgICAgICBsb2FkTWFpbnRlbmFuY2VEb2N1bWVudHMoKVxyXG4gICAgICAgICAgICBsb2FkQ2xpZW50RG9jdW1lbnRzKClcclxuICAgICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgICAgIH1cclxuICAgIH0sIFtpc0xvYWRpbmddKVxyXG5cclxuICAgIGNvbnN0IGNvbHVtbnMgPSBjcmVhdGVDb2x1bW5zKFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAnbmFtZScsXHJcbiAgICAgICAgICAgIGhlYWRlcjogKHsgY29sdW1uIH06IHsgY29sdW1uOiBhbnkgfSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgPERhdGFUYWJsZVNvcnRIZWFkZXIgY29sdW1uPXtjb2x1bW59IHRpdGxlPVwiTmFtZVwiIC8+XHJcbiAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdsZWZ0JyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZG9jdW1lbnQ6IGFueSA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9jZXNzLmVudi5GSUxFX0JBU0VfVVJMICtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuZmlsZUZpbGVuYW1lXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldD1cIl9ibGFua1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gaG92ZXI6dW5kZXJsaW5lIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2RvY3VtZW50Lm5hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEV4dGVybmFsTGluayBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWQ6aGlkZGVuXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2RvY3VtZW50LnR5cGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2RvY3VtZW50LnR5cGVfdGl0bGUgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtkb2N1bWVudC50eXBlX3RpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJsb2NrIG1kOmhpZGRlbiB0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQ3JlYXRlZDp7JyAnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2RvY3VtZW50Py5jcmVhdGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBmb3JtYXREYXRlKGRvY3VtZW50LmNyZWF0ZWQpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnTm8gRGF0ZSd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBzb3J0aW5nRm46IChyb3dBOiBhbnksIHJvd0I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID0gcm93QT8ub3JpZ2luYWw/Lm5hbWUgfHwgJydcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQiA9IHJvd0I/Lm9yaWdpbmFsPy5uYW1lIHx8ICcnXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ3R5cGUnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIk1vZHVsZVwiIC8+XHJcbiAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdsZWZ0JyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZG9jdW1lbnQgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6YmxvY2tcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHR5cGU9XCJub3JtYWxcIiB2YXJpYW50PVwib3V0bGluZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2RvY3VtZW50LnR5cGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHNvcnRpbmdGbjogKHJvd0E6IGFueSwgcm93QjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUEgPSByb3dBPy5vcmlnaW5hbD8udHlwZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVCID0gcm93Qj8ub3JpZ2luYWw/LnR5cGUgfHwgJydcclxuICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZUEubG9jYWxlQ29tcGFyZSh2YWx1ZUIpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAndHlwZV90aXRsZScsXHJcbiAgICAgICAgICAgIGhlYWRlcjogKHsgY29sdW1uIH06IHsgY29sdW1uOiBhbnkgfSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgPERhdGFUYWJsZVNvcnRIZWFkZXIgY29sdW1uPXtjb2x1bW59IHRpdGxlPVwiSXRlbVwiIC8+XHJcbiAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBkb2N1bWVudCA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBtZDpibG9jayB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHshaXNFbXB0eShkb2N1bWVudC50eXBlX3RpdGxlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBkb2N1bWVudC50eXBlX3RpdGxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICctJ31cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQSA9IHJvd0E/Lm9yaWdpbmFsPy50eXBlX3RpdGxlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUIgPSByb3dCPy5vcmlnaW5hbD8udHlwZV90aXRsZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlQS5sb2NhbGVDb21wYXJlKHZhbHVlQilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdjcmVhdGVkJyxcclxuICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJVcGxvYWQgZGF0ZVwiIC8+XHJcbiAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdyaWdodCcsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGRvY3VtZW50ID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmJsb2NrIHRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2RvY3VtZW50Py5jcmVhdGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGZvcm1hdERhdGUoZG9jdW1lbnQuY3JlYXRlZClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ05vIERhdGUnfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBzb3J0aW5nRm46IChyb3dBOiBhbnksIHJvd0I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZGF0ZUEgPSBuZXcgRGF0ZShyb3dBPy5vcmlnaW5hbD8uY3JlYXRlZCB8fCAwKS5nZXRUaW1lKClcclxuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGVCID0gbmV3IERhdGUocm93Qj8ub3JpZ2luYWw/LmNyZWF0ZWQgfHwgMCkuZ2V0VGltZSgpXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gZGF0ZUIgLSBkYXRlQVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICBdKVxyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPD5cclxuICAgICAgICAgICAgPExpc3RIZWFkZXJcclxuICAgICAgICAgICAgICAgIGljb249e1xyXG4gICAgICAgICAgICAgICAgICAgIDxTZWFsb2dzRG9jdW1lbnRMb2NrZXJJY29uIGNsYXNzTmFtZT1cImgtMTIgdy0xMiByaW5nLTEgcC0xIHJvdW5kZWQtZnVsbFwiIC8+XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB0aXRsZT1cIkRvY3VtZW50IGxvY2tlclwiXHJcbiAgICAgICAgICAgICAgICBhY3Rpb25zPXs8RG9jdW1lbnRMb2NrZXJGaWx0ZXJBY3Rpb25zIC8+fVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTE2XCI+XHJcbiAgICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxMb2FkaW5nIC8+XHJcbiAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgIDxEYXRhVGFibGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29sdW1ucz17Y29sdW1uc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YT17dmVzc2VsTGlzdFdpdGhEb2N1bWVudHN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNob3dUb29sYmFyPXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWdlU2l6ZT17MjB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVGaWx0ZXJPbkNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC8+XHJcbiAgICApXHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJMb2FkaW5nIiwidXNlTGF6eVF1ZXJ5IiwiVkVTU0VMX0xJU1RfV0lUSF9ET0NVTUVOVFMiLCJHRVRfSU5WRU5UT1JJRVNfV0lUSF9ET0NVTUVOVFMiLCJNQUlOVEVOQU5DRV9MSVNUX1dJVEhfRE9DVU1FTlQiLCJSRUFEX09ORV9DTElFTlQiLCJpc0VtcHR5IiwiZm9ybWF0RGF0ZSIsIkRhdGFUYWJsZSIsImNyZWF0ZUNvbHVtbnMiLCJEYXRhVGFibGVTb3J0SGVhZGVyIiwiTGlzdEhlYWRlciIsIlNlYWxvZ3NEb2N1bWVudExvY2tlckljb24iLCJCYWRnZSIsIkV4dGVybmFsTGluayIsIkRvY3VtZW50TG9ja2VyRmlsdGVyQWN0aW9ucyIsIkRvY3VtZW50TGlzdCIsInZlc3NlbExpc3RXaXRoRG9jdW1lbnRzIiwic2V0VmVzc2VsTGlzdFdpdGhEb2N1bWVudHMiLCJ2ZXNzZWxMaXN0V2l0aERvY3VtZW50c0NvcHkiLCJzZXRWZXNzZWxMaXN0V2l0aERvY3VtZW50c0NvcHkiLCJmaWx0ZXIiLCJzZXRGaWx0ZXIiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJxdWVyeVZlc3NlbExpc3REb2N1bWVudCIsImZldGNoUG9saWN5Iiwib25Db21wbGV0ZWQiLCJyZXNwb25zZSIsImZpbHRlckRhdGEiLCJkYXRhIiwicmVhZFZlc3NlbHMiLCJub2RlcyIsImxlbmd0aCIsImVsZW1lbnQiLCJkb2N1bWVudHMiLCJkb2MiLCJtb2RpZmllZERvYyIsInR5cGUiLCJfX3R5cGVuYW1lIiwidHlwZV90aXRsZSIsInRpdGxlIiwidHlwZV9pZCIsImlkIiwicHVzaCIsIm9uRXJyb3IiLCJlcnJvciIsImNvbnNvbGUiLCJxdWVyeUludmVudG9yaWVzTGlzdERvY3VtZW50IiwicmVhZEludmVudG9yaWVzIiwicXVlcnlNYWludGVuYW5jZUxpc3REb2N1bWVudCIsInJlYWRDb21wb25lbnRNYWludGVuYW5jZUNoZWNrcyIsIm5hbWUiLCJoYW5kbGVGaWx0ZXJPbkNoYW5nZSIsInNlYXJjaEZpbHRlciIsInVwZGF0ZWRWZXNzZWxMaXN0IiwiQXJyYXkiLCJpc0FycmF5IiwidmVzc2VsSUQiLCJpbiIsIm1hcCIsIml0ZW0iLCJ2YWx1ZSIsImluY2x1ZGVzIiwiZXEiLCJ0b1N0cmluZyIsIm1vZHVsZU5hbWUiLCJ0cmltIiwibG93ZXJDYXNlQ29udGFpbnMiLCJ0b0xvd2VyQ2FzZSIsImNvbnRhaW5zIiwibG93ZXJDYXNlVGl0bGUiLCJsb3dlckNhc2VOYW1lIiwicmVhZE9uZUNsaWVudCIsImRvY3MiLCJsb2FkQ2xpZW50RG9jdW1lbnRzIiwibG9jYWxTdG9yYWdlIiwidmFyaWFibGVzIiwiZ2V0SXRlbSIsImxvYWRWZXNzZWxEb2N1bWVudHMiLCJsb2FkSW52ZW50b3J5RG9jdW1lbnRzIiwibG9hZE1haW50ZW5hbmNlRG9jdW1lbnRzIiwiY29sdW1ucyIsImFjY2Vzc29yS2V5IiwiaGVhZGVyIiwiY29sdW1uIiwiY2VsbEFsaWdubWVudCIsImNlbGwiLCJyb3ciLCJkb2N1bWVudCIsIm9yaWdpbmFsIiwiZGl2IiwiY2xhc3NOYW1lIiwiYSIsImhyZWYiLCJwcm9jZXNzIiwiZW52IiwiRklMRV9CQVNFX1VSTCIsImZpbGVGaWxlbmFtZSIsInRhcmdldCIsInJlbCIsInZhcmlhbnQiLCJzcGFuIiwiY3JlYXRlZCIsInNvcnRpbmdGbiIsInJvd0EiLCJyb3dCIiwidmFsdWVBIiwidmFsdWVCIiwibG9jYWxlQ29tcGFyZSIsImRhdGVBIiwiRGF0ZSIsImdldFRpbWUiLCJkYXRlQiIsImljb24iLCJhY3Rpb25zIiwic2hvd1Rvb2xiYXIiLCJwYWdlU2l6ZSIsIm9uQ2hhbmdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/document-locker/list.tsx\n"));

/***/ })

});