"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba";
exports.ids = ["vendor-chunks/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/DOMLayoutDelegate.mjs":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/DOMLayoutDelegate.mjs ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DOMLayoutDelegate: () => (/* binding */ $657e4dc4a6e88df0$export$8f5ed9ff9f511381)\n/* harmony export */ });\n/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ class $657e4dc4a6e88df0$export$8f5ed9ff9f511381 {\n    getItemRect(key) {\n        let container = this.ref.current;\n        if (!container) return null;\n        let item = key != null ? container.querySelector(`[data-key=\"${CSS.escape(key.toString())}\"]`) : null;\n        if (!item) return null;\n        let containerRect = container.getBoundingClientRect();\n        let itemRect = item.getBoundingClientRect();\n        return {\n            x: itemRect.left - containerRect.left + container.scrollLeft,\n            y: itemRect.top - containerRect.top + container.scrollTop,\n            width: itemRect.width,\n            height: itemRect.height\n        };\n    }\n    getContentSize() {\n        let container = this.ref.current;\n        var _container_scrollWidth, _container_scrollHeight;\n        return {\n            width: (_container_scrollWidth = container === null || container === void 0 ? void 0 : container.scrollWidth) !== null && _container_scrollWidth !== void 0 ? _container_scrollWidth : 0,\n            height: (_container_scrollHeight = container === null || container === void 0 ? void 0 : container.scrollHeight) !== null && _container_scrollHeight !== void 0 ? _container_scrollHeight : 0\n        };\n    }\n    getVisibleRect() {\n        let container = this.ref.current;\n        var _container_scrollLeft, _container_scrollTop, _container_offsetWidth, _container_offsetHeight;\n        return {\n            x: (_container_scrollLeft = container === null || container === void 0 ? void 0 : container.scrollLeft) !== null && _container_scrollLeft !== void 0 ? _container_scrollLeft : 0,\n            y: (_container_scrollTop = container === null || container === void 0 ? void 0 : container.scrollTop) !== null && _container_scrollTop !== void 0 ? _container_scrollTop : 0,\n            width: (_container_offsetWidth = container === null || container === void 0 ? void 0 : container.offsetWidth) !== null && _container_offsetWidth !== void 0 ? _container_offsetWidth : 0,\n            height: (_container_offsetHeight = container === null || container === void 0 ? void 0 : container.offsetHeight) !== null && _container_offsetHeight !== void 0 ? _container_offsetHeight : 0\n        };\n    }\n    constructor(ref){\n        this.ref = ref;\n    }\n}\n\n\n\n//# sourceMappingURL=DOMLayoutDelegate.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/DOMLayoutDelegate.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/ListKeyboardDelegate.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/ListKeyboardDelegate.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListKeyboardDelegate: () => (/* binding */ $2a25aae57d74318e$export$a05409b8bb224a5a)\n/* harmony export */ });\n/* harmony import */ var _DOMLayoutDelegate_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DOMLayoutDelegate.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/DOMLayoutDelegate.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/isScrollable.mjs\");\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\nclass $2a25aae57d74318e$export$a05409b8bb224a5a {\n    isDisabled(item) {\n        var _item_props;\n        return this.disabledBehavior === 'all' && (((_item_props = item.props) === null || _item_props === void 0 ? void 0 : _item_props.isDisabled) || this.disabledKeys.has(item.key));\n    }\n    findNextNonDisabled(key, getNext) {\n        let nextKey = key;\n        while(nextKey != null){\n            let item = this.collection.getItem(nextKey);\n            if ((item === null || item === void 0 ? void 0 : item.type) === 'item' && !this.isDisabled(item)) return nextKey;\n            nextKey = getNext(nextKey);\n        }\n        return null;\n    }\n    getNextKey(key) {\n        let nextKey = key;\n        nextKey = this.collection.getKeyAfter(nextKey);\n        return this.findNextNonDisabled(nextKey, (key)=>this.collection.getKeyAfter(key));\n    }\n    getPreviousKey(key) {\n        let nextKey = key;\n        nextKey = this.collection.getKeyBefore(nextKey);\n        return this.findNextNonDisabled(nextKey, (key)=>this.collection.getKeyBefore(key));\n    }\n    findKey(key, nextKey, shouldSkip) {\n        let tempKey = key;\n        let itemRect = this.layoutDelegate.getItemRect(tempKey);\n        if (!itemRect || tempKey == null) return null;\n        // Find the item above or below in the same column.\n        let prevRect = itemRect;\n        do {\n            tempKey = nextKey(tempKey);\n            if (tempKey == null) break;\n            itemRect = this.layoutDelegate.getItemRect(tempKey);\n        }while (itemRect && shouldSkip(prevRect, itemRect) && tempKey != null);\n        return tempKey;\n    }\n    isSameRow(prevRect, itemRect) {\n        return prevRect.y === itemRect.y || prevRect.x !== itemRect.x;\n    }\n    isSameColumn(prevRect, itemRect) {\n        return prevRect.x === itemRect.x || prevRect.y !== itemRect.y;\n    }\n    getKeyBelow(key) {\n        if (this.layout === 'grid' && this.orientation === 'vertical') return this.findKey(key, (key)=>this.getNextKey(key), this.isSameRow);\n        else return this.getNextKey(key);\n    }\n    getKeyAbove(key) {\n        if (this.layout === 'grid' && this.orientation === 'vertical') return this.findKey(key, (key)=>this.getPreviousKey(key), this.isSameRow);\n        else return this.getPreviousKey(key);\n    }\n    getNextColumn(key, right) {\n        return right ? this.getPreviousKey(key) : this.getNextKey(key);\n    }\n    getKeyRightOf(key) {\n        // This is a temporary solution for CardView until we refactor useSelectableCollection.\n        // https://github.com/orgs/adobe/projects/19/views/32?pane=issue&itemId=77825042\n        let layoutDelegateMethod = this.direction === 'ltr' ? 'getKeyRightOf' : 'getKeyLeftOf';\n        if (this.layoutDelegate[layoutDelegateMethod]) {\n            key = this.layoutDelegate[layoutDelegateMethod](key);\n            return this.findNextNonDisabled(key, (key)=>this.layoutDelegate[layoutDelegateMethod](key));\n        }\n        if (this.layout === 'grid') {\n            if (this.orientation === 'vertical') return this.getNextColumn(key, this.direction === 'rtl');\n            else return this.findKey(key, (key)=>this.getNextColumn(key, this.direction === 'rtl'), this.isSameColumn);\n        } else if (this.orientation === 'horizontal') return this.getNextColumn(key, this.direction === 'rtl');\n        return null;\n    }\n    getKeyLeftOf(key) {\n        let layoutDelegateMethod = this.direction === 'ltr' ? 'getKeyLeftOf' : 'getKeyRightOf';\n        if (this.layoutDelegate[layoutDelegateMethod]) {\n            key = this.layoutDelegate[layoutDelegateMethod](key);\n            return this.findNextNonDisabled(key, (key)=>this.layoutDelegate[layoutDelegateMethod](key));\n        }\n        if (this.layout === 'grid') {\n            if (this.orientation === 'vertical') return this.getNextColumn(key, this.direction === 'ltr');\n            else return this.findKey(key, (key)=>this.getNextColumn(key, this.direction === 'ltr'), this.isSameColumn);\n        } else if (this.orientation === 'horizontal') return this.getNextColumn(key, this.direction === 'ltr');\n        return null;\n    }\n    getFirstKey() {\n        let key = this.collection.getFirstKey();\n        return this.findNextNonDisabled(key, (key)=>this.collection.getKeyAfter(key));\n    }\n    getLastKey() {\n        let key = this.collection.getLastKey();\n        return this.findNextNonDisabled(key, (key)=>this.collection.getKeyBefore(key));\n    }\n    getKeyPageAbove(key) {\n        let menu = this.ref.current;\n        let itemRect = this.layoutDelegate.getItemRect(key);\n        if (!itemRect) return null;\n        if (menu && !(0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.isScrollable)(menu)) return this.getFirstKey();\n        let nextKey = key;\n        if (this.orientation === 'horizontal') {\n            let pageX = Math.max(0, itemRect.x + itemRect.width - this.layoutDelegate.getVisibleRect().width);\n            while(itemRect && itemRect.x > pageX && nextKey != null){\n                nextKey = this.getKeyAbove(nextKey);\n                itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n            }\n        } else {\n            let pageY = Math.max(0, itemRect.y + itemRect.height - this.layoutDelegate.getVisibleRect().height);\n            while(itemRect && itemRect.y > pageY && nextKey != null){\n                nextKey = this.getKeyAbove(nextKey);\n                itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n            }\n        }\n        return nextKey !== null && nextKey !== void 0 ? nextKey : this.getFirstKey();\n    }\n    getKeyPageBelow(key) {\n        let menu = this.ref.current;\n        let itemRect = this.layoutDelegate.getItemRect(key);\n        if (!itemRect) return null;\n        if (menu && !(0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.isScrollable)(menu)) return this.getLastKey();\n        let nextKey = key;\n        if (this.orientation === 'horizontal') {\n            let pageX = Math.min(this.layoutDelegate.getContentSize().width, itemRect.y - itemRect.width + this.layoutDelegate.getVisibleRect().width);\n            while(itemRect && itemRect.x < pageX && nextKey != null){\n                nextKey = this.getKeyBelow(nextKey);\n                itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n            }\n        } else {\n            let pageY = Math.min(this.layoutDelegate.getContentSize().height, itemRect.y - itemRect.height + this.layoutDelegate.getVisibleRect().height);\n            while(itemRect && itemRect.y < pageY && nextKey != null){\n                nextKey = this.getKeyBelow(nextKey);\n                itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n            }\n        }\n        return nextKey !== null && nextKey !== void 0 ? nextKey : this.getLastKey();\n    }\n    getKeyForSearch(search, fromKey) {\n        if (!this.collator) return null;\n        let collection = this.collection;\n        let key = fromKey || this.getFirstKey();\n        while(key != null){\n            let item = collection.getItem(key);\n            if (!item) return null;\n            let substring = item.textValue.slice(0, search.length);\n            if (item.textValue && this.collator.compare(substring, search) === 0) return key;\n            key = this.getNextKey(key);\n        }\n        return null;\n    }\n    constructor(...args){\n        if (args.length === 1) {\n            let opts = args[0];\n            this.collection = opts.collection;\n            this.ref = opts.ref;\n            this.collator = opts.collator;\n            this.disabledKeys = opts.disabledKeys || new Set();\n            this.disabledBehavior = opts.disabledBehavior || 'all';\n            this.orientation = opts.orientation || 'vertical';\n            this.direction = opts.direction;\n            this.layout = opts.layout || 'stack';\n            this.layoutDelegate = opts.layoutDelegate || new (0, _DOMLayoutDelegate_mjs__WEBPACK_IMPORTED_MODULE_1__.DOMLayoutDelegate)(opts.ref);\n        } else {\n            this.collection = args[0];\n            this.disabledKeys = args[1];\n            this.ref = args[2];\n            this.collator = args[3];\n            this.layout = 'stack';\n            this.orientation = 'vertical';\n            this.disabledBehavior = 'all';\n            this.layoutDelegate = new (0, _DOMLayoutDelegate_mjs__WEBPACK_IMPORTED_MODULE_1__.DOMLayoutDelegate)(this.ref);\n        }\n        // If this is a vertical stack, remove the left/right methods completely\n        // so they aren't called by useDroppableCollection.\n        if (this.layout === 'stack' && this.orientation === 'vertical') {\n            this.getKeyLeftOf = undefined;\n            this.getKeyRightOf = undefined;\n        }\n    }\n}\n\n\n\n//# sourceMappingURL=ListKeyboardDelegate.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/ListKeyboardDelegate.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/useSelectableCollection.mjs":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/useSelectableCollection.mjs ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSelectableCollection: () => (/* binding */ $ae20dd8cbca75726$export$d6daf82dcd84e87c)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/utils.mjs\");\n/* harmony import */ var _useTypeSelect_mjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./useTypeSelect.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/useTypeSelect.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/openLink.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/keyboard.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/focusWithoutScrolling.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/useEvent.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/scrollIntoView.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/constants.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/useEffectEvent.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/useUpdateLayoutEffect.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/mergeProps.mjs\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_aria_focus__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @react-aria/focus */ \"(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/FocusScope.mjs\");\n/* harmony import */ var _react_aria_focus__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @react-aria/focus */ \"(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/focusSafely.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._697c5b52c6876ffd3e2260945ec4674e/node_modules/@react-aria/interactions/dist/useFocusVisible.mjs\");\n/* harmony import */ var _react_aria_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/i18n */ \"(ssr)/./node_modules/.pnpm/@react-aria+i18n@3.12.5_rea_c3394d9e3bca4b1a38969e4ba9336700/node_modules/@react-aria/i18n/dist/context.mjs\");\n\n\n\n\n\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n\n\n\n\n\nfunction $ae20dd8cbca75726$export$d6daf82dcd84e87c(options) {\n    let { selectionManager: manager, keyboardDelegate: delegate, ref: ref, autoFocus: autoFocus = false, shouldFocusWrap: shouldFocusWrap = false, disallowEmptySelection: disallowEmptySelection = false, disallowSelectAll: disallowSelectAll = false, selectOnFocus: selectOnFocus = manager.selectionBehavior === 'replace', disallowTypeAhead: disallowTypeAhead = false, shouldUseVirtualFocus: shouldUseVirtualFocus, allowsTabNavigation: allowsTabNavigation = false, isVirtualized: isVirtualized, scrollRef: // If no scrollRef is provided, assume the collection ref is the scrollable region\n    scrollRef = ref, linkBehavior: linkBehavior = 'action' } = options;\n    let { direction: direction } = (0, _react_aria_i18n__WEBPACK_IMPORTED_MODULE_2__.useLocale)();\n    let router = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    let onKeyDown = (e)=>{\n        var _ref_current;\n        // Prevent option + tab from doing anything since it doesn't move focus to the cells, only buttons/checkboxes\n        if (e.altKey && e.key === 'Tab') e.preventDefault();\n        // Keyboard events bubble through portals. Don't handle keyboard events\n        // for elements outside the collection (e.g. menus).\n        if (!((_ref_current = ref.current) === null || _ref_current === void 0 ? void 0 : _ref_current.contains(e.target))) return;\n        const navigateToKey = (key, childFocus)=>{\n            if (key != null) {\n                if (manager.isLink(key) && linkBehavior === 'selection' && selectOnFocus && !(0, _utils_mjs__WEBPACK_IMPORTED_MODULE_4__.isNonContiguousSelectionModifier)(e)) {\n                    var _scrollRef_current;\n                    // Set focused key and re-render synchronously to bring item into view if needed.\n                    (0, react_dom__WEBPACK_IMPORTED_MODULE_0__.flushSync)(()=>{\n                        manager.setFocusedKey(key, childFocus);\n                    });\n                    let item = (_scrollRef_current = scrollRef.current) === null || _scrollRef_current === void 0 ? void 0 : _scrollRef_current.querySelector(`[data-key=\"${CSS.escape(key.toString())}\"]`);\n                    let itemProps = manager.getItemProps(key);\n                    if (item) router.open(item, e, itemProps.href, itemProps.routerOptions);\n                    return;\n                }\n                manager.setFocusedKey(key, childFocus);\n                if (manager.isLink(key) && linkBehavior === 'override') return;\n                if (e.shiftKey && manager.selectionMode === 'multiple') manager.extendSelection(key);\n                else if (selectOnFocus && !(0, _utils_mjs__WEBPACK_IMPORTED_MODULE_4__.isNonContiguousSelectionModifier)(e)) manager.replaceSelection(key);\n            }\n        };\n        switch(e.key){\n            case 'ArrowDown':\n                if (delegate.getKeyBelow) {\n                    var _delegate_getKeyBelow, _delegate_getFirstKey, _delegate_getFirstKey1;\n                    let nextKey = manager.focusedKey != null ? (_delegate_getKeyBelow = delegate.getKeyBelow) === null || _delegate_getKeyBelow === void 0 ? void 0 : _delegate_getKeyBelow.call(delegate, manager.focusedKey) : (_delegate_getFirstKey = delegate.getFirstKey) === null || _delegate_getFirstKey === void 0 ? void 0 : _delegate_getFirstKey.call(delegate);\n                    if (nextKey == null && shouldFocusWrap) nextKey = (_delegate_getFirstKey1 = delegate.getFirstKey) === null || _delegate_getFirstKey1 === void 0 ? void 0 : _delegate_getFirstKey1.call(delegate, manager.focusedKey);\n                    if (nextKey != null) {\n                        e.preventDefault();\n                        navigateToKey(nextKey);\n                    }\n                }\n                break;\n            case 'ArrowUp':\n                if (delegate.getKeyAbove) {\n                    var _delegate_getKeyAbove, _delegate_getLastKey, _delegate_getLastKey1;\n                    let nextKey = manager.focusedKey != null ? (_delegate_getKeyAbove = delegate.getKeyAbove) === null || _delegate_getKeyAbove === void 0 ? void 0 : _delegate_getKeyAbove.call(delegate, manager.focusedKey) : (_delegate_getLastKey = delegate.getLastKey) === null || _delegate_getLastKey === void 0 ? void 0 : _delegate_getLastKey.call(delegate);\n                    if (nextKey == null && shouldFocusWrap) nextKey = (_delegate_getLastKey1 = delegate.getLastKey) === null || _delegate_getLastKey1 === void 0 ? void 0 : _delegate_getLastKey1.call(delegate, manager.focusedKey);\n                    if (nextKey != null) {\n                        e.preventDefault();\n                        navigateToKey(nextKey);\n                    }\n                }\n                break;\n            case 'ArrowLeft':\n                if (delegate.getKeyLeftOf) {\n                    var _delegate_getKeyLeftOf, _delegate_getFirstKey2, _delegate_getLastKey2;\n                    let nextKey = manager.focusedKey != null ? (_delegate_getKeyLeftOf = delegate.getKeyLeftOf) === null || _delegate_getKeyLeftOf === void 0 ? void 0 : _delegate_getKeyLeftOf.call(delegate, manager.focusedKey) : null;\n                    if (nextKey == null && shouldFocusWrap) nextKey = direction === 'rtl' ? (_delegate_getFirstKey2 = delegate.getFirstKey) === null || _delegate_getFirstKey2 === void 0 ? void 0 : _delegate_getFirstKey2.call(delegate, manager.focusedKey) : (_delegate_getLastKey2 = delegate.getLastKey) === null || _delegate_getLastKey2 === void 0 ? void 0 : _delegate_getLastKey2.call(delegate, manager.focusedKey);\n                    if (nextKey != null) {\n                        e.preventDefault();\n                        navigateToKey(nextKey, direction === 'rtl' ? 'first' : 'last');\n                    }\n                }\n                break;\n            case 'ArrowRight':\n                if (delegate.getKeyRightOf) {\n                    var _delegate_getKeyRightOf, _delegate_getLastKey3, _delegate_getFirstKey3;\n                    let nextKey = manager.focusedKey != null ? (_delegate_getKeyRightOf = delegate.getKeyRightOf) === null || _delegate_getKeyRightOf === void 0 ? void 0 : _delegate_getKeyRightOf.call(delegate, manager.focusedKey) : null;\n                    if (nextKey == null && shouldFocusWrap) nextKey = direction === 'rtl' ? (_delegate_getLastKey3 = delegate.getLastKey) === null || _delegate_getLastKey3 === void 0 ? void 0 : _delegate_getLastKey3.call(delegate, manager.focusedKey) : (_delegate_getFirstKey3 = delegate.getFirstKey) === null || _delegate_getFirstKey3 === void 0 ? void 0 : _delegate_getFirstKey3.call(delegate, manager.focusedKey);\n                    if (nextKey != null) {\n                        e.preventDefault();\n                        navigateToKey(nextKey, direction === 'rtl' ? 'last' : 'first');\n                    }\n                }\n                break;\n            case 'Home':\n                if (delegate.getFirstKey) {\n                    if (manager.focusedKey === null && e.shiftKey) return;\n                    e.preventDefault();\n                    let firstKey = delegate.getFirstKey(manager.focusedKey, (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.isCtrlKeyPressed)(e));\n                    manager.setFocusedKey(firstKey);\n                    if (firstKey != null) {\n                        if ((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.isCtrlKeyPressed)(e) && e.shiftKey && manager.selectionMode === 'multiple') manager.extendSelection(firstKey);\n                        else if (selectOnFocus) manager.replaceSelection(firstKey);\n                    }\n                }\n                break;\n            case 'End':\n                if (delegate.getLastKey) {\n                    if (manager.focusedKey === null && e.shiftKey) return;\n                    e.preventDefault();\n                    let lastKey = delegate.getLastKey(manager.focusedKey, (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.isCtrlKeyPressed)(e));\n                    manager.setFocusedKey(lastKey);\n                    if (lastKey != null) {\n                        if ((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.isCtrlKeyPressed)(e) && e.shiftKey && manager.selectionMode === 'multiple') manager.extendSelection(lastKey);\n                        else if (selectOnFocus) manager.replaceSelection(lastKey);\n                    }\n                }\n                break;\n            case 'PageDown':\n                if (delegate.getKeyPageBelow && manager.focusedKey != null) {\n                    let nextKey = delegate.getKeyPageBelow(manager.focusedKey);\n                    if (nextKey != null) {\n                        e.preventDefault();\n                        navigateToKey(nextKey);\n                    }\n                }\n                break;\n            case 'PageUp':\n                if (delegate.getKeyPageAbove && manager.focusedKey != null) {\n                    let nextKey = delegate.getKeyPageAbove(manager.focusedKey);\n                    if (nextKey != null) {\n                        e.preventDefault();\n                        navigateToKey(nextKey);\n                    }\n                }\n                break;\n            case 'a':\n                if ((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.isCtrlKeyPressed)(e) && manager.selectionMode === 'multiple' && disallowSelectAll !== true) {\n                    e.preventDefault();\n                    manager.selectAll();\n                }\n                break;\n            case 'Escape':\n                if (!disallowEmptySelection && manager.selectedKeys.size !== 0) {\n                    e.stopPropagation();\n                    e.preventDefault();\n                    manager.clearSelection();\n                }\n                break;\n            case 'Tab':\n                if (!allowsTabNavigation) {\n                    // There may be elements that are \"tabbable\" inside a collection (e.g. in a grid cell).\n                    // However, collections should be treated as a single tab stop, with arrow key navigation internally.\n                    // We don't control the rendering of these, so we can't override the tabIndex to prevent tabbing.\n                    // Instead, we handle the Tab key, and move focus manually to the first/last tabbable element\n                    // in the collection, so that the browser default behavior will apply starting from that element\n                    // rather than the currently focused one.\n                    if (e.shiftKey) ref.current.focus();\n                    else {\n                        let walker = (0, _react_aria_focus__WEBPACK_IMPORTED_MODULE_6__.getFocusableTreeWalker)(ref.current, {\n                            tabbable: true\n                        });\n                        let next = undefined;\n                        let last;\n                        do {\n                            last = walker.lastChild();\n                            if (last) next = last;\n                        }while (last);\n                        if (next && !next.contains(document.activeElement)) (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_7__.focusWithoutScrolling)(next);\n                    }\n                    break;\n                }\n        }\n    };\n    // Store the scroll position so we can restore it later.\n    /// TODO: should this happen all the time??\n    let scrollPos = (0, react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        top: 0,\n        left: 0\n    });\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_8__.useEvent)(scrollRef, 'scroll', isVirtualized ? undefined : ()=>{\n        var _scrollRef_current, _scrollRef_current1;\n        var _scrollRef_current_scrollTop, _scrollRef_current_scrollLeft;\n        scrollPos.current = {\n            top: (_scrollRef_current_scrollTop = (_scrollRef_current = scrollRef.current) === null || _scrollRef_current === void 0 ? void 0 : _scrollRef_current.scrollTop) !== null && _scrollRef_current_scrollTop !== void 0 ? _scrollRef_current_scrollTop : 0,\n            left: (_scrollRef_current_scrollLeft = (_scrollRef_current1 = scrollRef.current) === null || _scrollRef_current1 === void 0 ? void 0 : _scrollRef_current1.scrollLeft) !== null && _scrollRef_current_scrollLeft !== void 0 ? _scrollRef_current_scrollLeft : 0\n        };\n    });\n    let onFocus = (e)=>{\n        if (manager.isFocused) {\n            // If a focus event bubbled through a portal, reset focus state.\n            if (!e.currentTarget.contains(e.target)) manager.setFocused(false);\n            return;\n        }\n        // Focus events can bubble through portals. Ignore these events.\n        if (!e.currentTarget.contains(e.target)) return;\n        manager.setFocused(true);\n        if (manager.focusedKey == null) {\n            var _delegate_getLastKey, _delegate_getFirstKey;\n            let navigateToFirstKey = (key)=>{\n                if (key != null) {\n                    manager.setFocusedKey(key);\n                    if (selectOnFocus) manager.replaceSelection(key);\n                }\n            };\n            // If the user hasn't yet interacted with the collection, there will be no focusedKey set.\n            // Attempt to detect whether the user is tabbing forward or backward into the collection\n            // and either focus the first or last item accordingly.\n            let relatedTarget = e.relatedTarget;\n            var _manager_lastSelectedKey, _manager_firstSelectedKey;\n            if (relatedTarget && e.currentTarget.compareDocumentPosition(relatedTarget) & Node.DOCUMENT_POSITION_FOLLOWING) navigateToFirstKey((_manager_lastSelectedKey = manager.lastSelectedKey) !== null && _manager_lastSelectedKey !== void 0 ? _manager_lastSelectedKey : (_delegate_getLastKey = delegate.getLastKey) === null || _delegate_getLastKey === void 0 ? void 0 : _delegate_getLastKey.call(delegate));\n            else navigateToFirstKey((_manager_firstSelectedKey = manager.firstSelectedKey) !== null && _manager_firstSelectedKey !== void 0 ? _manager_firstSelectedKey : (_delegate_getFirstKey = delegate.getFirstKey) === null || _delegate_getFirstKey === void 0 ? void 0 : _delegate_getFirstKey.call(delegate));\n        } else if (!isVirtualized && scrollRef.current) {\n            // Restore the scroll position to what it was before.\n            scrollRef.current.scrollTop = scrollPos.current.top;\n            scrollRef.current.scrollLeft = scrollPos.current.left;\n        }\n        if (manager.focusedKey != null && scrollRef.current) {\n            // Refocus and scroll the focused item into view if it exists within the scrollable region.\n            let element = scrollRef.current.querySelector(`[data-key=\"${CSS.escape(manager.focusedKey.toString())}\"]`);\n            if (element) {\n                // This prevents a flash of focus on the first/last element in the collection, or the collection itself.\n                if (!element.contains(document.activeElement)) (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_7__.focusWithoutScrolling)(element);\n                let modality = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_9__.getInteractionModality)();\n                if (modality === 'keyboard') (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_10__.scrollIntoViewport)(element, {\n                    containingElement: ref.current\n                });\n            }\n        }\n    };\n    let onBlur = (e)=>{\n        // Don't set blurred and then focused again if moving focus within the collection.\n        if (!e.currentTarget.contains(e.relatedTarget)) manager.setFocused(false);\n    };\n    // Ref to track whether the first item in the collection should be automatically focused. Specifically used for autocomplete when user types\n    // to focus the first key AFTER the collection updates.\n    // TODO: potentially expand the usage of this\n    let shouldVirtualFocusFirst = (0, react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Add event listeners for custom virtual events. These handle updating the focused key in response to various keyboard events\n    // at the autocomplete level\n    // TODO: fix type later\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_8__.useEvent)(ref, (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_11__.FOCUS_EVENT), !shouldUseVirtualFocus ? undefined : (e)=>{\n        let { detail: detail } = e;\n        e.stopPropagation();\n        manager.setFocused(true);\n        // If the user is typing forwards, autofocus the first option in the list.\n        if ((detail === null || detail === void 0 ? void 0 : detail.focusStrategy) === 'first') shouldVirtualFocusFirst.current = true;\n    });\n    let updateActiveDescendant = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_12__.useEffectEvent)(()=>{\n        var _delegate_getFirstKey;\n        var _delegate_getFirstKey1;\n        let keyToFocus = (_delegate_getFirstKey1 = (_delegate_getFirstKey = delegate.getFirstKey) === null || _delegate_getFirstKey === void 0 ? void 0 : _delegate_getFirstKey.call(delegate)) !== null && _delegate_getFirstKey1 !== void 0 ? _delegate_getFirstKey1 : null;\n        // If no focusable items exist in the list, make sure to clear any activedescendant that may still exist\n        if (keyToFocus == null) {\n            var _ref_current;\n            (_ref_current = ref.current) === null || _ref_current === void 0 ? void 0 : _ref_current.dispatchEvent(new CustomEvent((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_11__.UPDATE_ACTIVEDESCENDANT), {\n                cancelable: true,\n                bubbles: true\n            }));\n            // If there wasn't a focusable key but the collection had items, then that means we aren't in an intermediate load state and all keys are disabled.\n            // Reset shouldVirtualFocusFirst so that we don't erronously autofocus an item when the collection is filtered again.\n            if (manager.collection.size > 0) shouldVirtualFocusFirst.current = false;\n        } else {\n            manager.setFocusedKey(keyToFocus);\n            // Only set shouldVirtualFocusFirst to false if we've successfully set the first key as the focused key\n            // If there wasn't a key to focus, we might be in a temporary loading state so we'll want to still focus the first key\n            // after the collection updates after load\n            shouldVirtualFocusFirst.current = false;\n        }\n    });\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_13__.useUpdateLayoutEffect)(()=>{\n        if (shouldVirtualFocusFirst.current) updateActiveDescendant();\n    }, [\n        manager.collection,\n        updateActiveDescendant\n    ]);\n    let resetFocusFirstFlag = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_12__.useEffectEvent)(()=>{\n        // If user causes the focused key to change in any other way, clear shouldVirtualFocusFirst so we don't\n        // accidentally move focus from under them. Skip this if the collection was empty because we might be in a load\n        // state and will still want to focus the first item after load\n        if (manager.collection.size > 0) shouldVirtualFocusFirst.current = false;\n    });\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_13__.useUpdateLayoutEffect)(()=>{\n        resetFocusFirstFlag();\n    }, [\n        manager.focusedKey,\n        resetFocusFirstFlag\n    ]);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_8__.useEvent)(ref, (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_11__.CLEAR_FOCUS_EVENT), !shouldUseVirtualFocus ? undefined : (e)=>{\n        e.stopPropagation();\n        manager.setFocused(false);\n        manager.setFocusedKey(null);\n    });\n    const autoFocusRef = (0, react__WEBPACK_IMPORTED_MODULE_1__.useRef)(autoFocus);\n    (0, react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (autoFocusRef.current) {\n            var _delegate_getFirstKey, _delegate_getLastKey;\n            let focusedKey = null;\n            var _delegate_getFirstKey1;\n            // Check focus strategy to determine which item to focus\n            if (autoFocus === 'first') focusedKey = (_delegate_getFirstKey1 = (_delegate_getFirstKey = delegate.getFirstKey) === null || _delegate_getFirstKey === void 0 ? void 0 : _delegate_getFirstKey.call(delegate)) !== null && _delegate_getFirstKey1 !== void 0 ? _delegate_getFirstKey1 : null;\n            var _delegate_getLastKey1;\n            if (autoFocus === 'last') focusedKey = (_delegate_getLastKey1 = (_delegate_getLastKey = delegate.getLastKey) === null || _delegate_getLastKey === void 0 ? void 0 : _delegate_getLastKey.call(delegate)) !== null && _delegate_getLastKey1 !== void 0 ? _delegate_getLastKey1 : null;\n            // If there are any selected keys, make the first one the new focus target\n            let selectedKeys = manager.selectedKeys;\n            if (selectedKeys.size) {\n                for (let key of selectedKeys)if (manager.canSelectItem(key)) {\n                    focusedKey = key;\n                    break;\n                }\n            }\n            manager.setFocused(true);\n            manager.setFocusedKey(focusedKey);\n            // If no default focus key is selected, focus the collection itself.\n            if (focusedKey == null && !shouldUseVirtualFocus && ref.current) (0, _react_aria_focus__WEBPACK_IMPORTED_MODULE_14__.focusSafely)(ref.current);\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    // Scroll the focused element into view when the focusedKey changes.\n    let lastFocusedKey = (0, react__WEBPACK_IMPORTED_MODULE_1__.useRef)(manager.focusedKey);\n    (0, react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (manager.isFocused && manager.focusedKey != null && (manager.focusedKey !== lastFocusedKey.current || autoFocusRef.current) && scrollRef.current && ref.current) {\n            let modality = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_9__.getInteractionModality)();\n            let element = ref.current.querySelector(`[data-key=\"${CSS.escape(manager.focusedKey.toString())}\"]`);\n            if (!element) // If item element wasn't found, return early (don't update autoFocusRef and lastFocusedKey).\n            // The collection may initially be empty (e.g. virtualizer), so wait until the element exists.\n            return;\n            if (modality === 'keyboard' || autoFocusRef.current) {\n                (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_10__.scrollIntoView)(scrollRef.current, element);\n                // Avoid scroll in iOS VO, since it may cause overlay to close (i.e. RAC submenu)\n                if (modality !== 'virtual') (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_10__.scrollIntoViewport)(element, {\n                    containingElement: ref.current\n                });\n            }\n        }\n        // If the focused key becomes null (e.g. the last item is deleted), focus the whole collection.\n        if (!shouldUseVirtualFocus && manager.isFocused && manager.focusedKey == null && lastFocusedKey.current != null && ref.current) (0, _react_aria_focus__WEBPACK_IMPORTED_MODULE_14__.focusSafely)(ref.current);\n        lastFocusedKey.current = manager.focusedKey;\n        autoFocusRef.current = false;\n    });\n    // Intercept FocusScope restoration since virtualized collections can reuse DOM nodes.\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_8__.useEvent)(ref, 'react-aria-focus-scope-restore', (e)=>{\n        e.preventDefault();\n        manager.setFocused(true);\n    });\n    let handlers = {\n        onKeyDown: onKeyDown,\n        onFocus: onFocus,\n        onBlur: onBlur,\n        onMouseDown (e) {\n            // Ignore events that bubbled through portals.\n            if (scrollRef.current === e.target) // Prevent focus going to the collection when clicking on the scrollbar.\n            e.preventDefault();\n        }\n    };\n    let { typeSelectProps: typeSelectProps } = (0, _useTypeSelect_mjs__WEBPACK_IMPORTED_MODULE_15__.useTypeSelect)({\n        keyboardDelegate: delegate,\n        selectionManager: manager\n    });\n    if (!disallowTypeAhead) handlers = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_16__.mergeProps)(typeSelectProps, handlers);\n    // If nothing is focused within the collection, make the collection itself tabbable.\n    // This will be marshalled to either the first or last item depending on where focus came from.\n    let tabIndex = undefined;\n    if (!shouldUseVirtualFocus) tabIndex = manager.focusedKey == null ? 0 : -1;\n    else tabIndex = -1;\n    return {\n        collectionProps: {\n            ...handlers,\n            tabIndex: tabIndex\n        }\n    };\n}\n\n\n\n//# sourceMappingURL=useSelectableCollection.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/useSelectableCollection.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/useSelectableItem.mjs":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/useSelectableItem.mjs ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSelectableItem: () => (/* binding */ $880e95eb8b93ba9a$export$ecf600387e221c37)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/utils.mjs\");\n/* harmony import */ var _react_aria_focus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-aria/focus */ \"(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/focusSafely.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/openLink.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/useId.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/keyboard.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/constants.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/mergeProps.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._697c5b52c6876ffd3e2260945ec4674e/node_modules/@react-aria/interactions/dist/usePress.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._697c5b52c6876ffd3e2260945ec4674e/node_modules/@react-aria/interactions/dist/useLongPress.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n\n\nfunction $880e95eb8b93ba9a$export$ecf600387e221c37(options) {\n    let { id: id, selectionManager: manager, key: key, ref: ref, shouldSelectOnPressUp: shouldSelectOnPressUp, shouldUseVirtualFocus: shouldUseVirtualFocus, focus: focus, isDisabled: isDisabled, onAction: onAction, allowsDifferentPressOrigin: allowsDifferentPressOrigin, linkBehavior: linkBehavior = 'action' } = options;\n    let router = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    id = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.useId)(id);\n    let onSelect = (e)=>{\n        if (e.pointerType === 'keyboard' && (0, _utils_mjs__WEBPACK_IMPORTED_MODULE_3__.isNonContiguousSelectionModifier)(e)) manager.toggleSelection(key);\n        else {\n            if (manager.selectionMode === 'none') return;\n            if (manager.isLink(key)) {\n                if (linkBehavior === 'selection' && ref.current) {\n                    let itemProps = manager.getItemProps(key);\n                    router.open(ref.current, e, itemProps.href, itemProps.routerOptions);\n                    // Always set selected keys back to what they were so that select and combobox close.\n                    manager.setSelectedKeys(manager.selectedKeys);\n                    return;\n                } else if (linkBehavior === 'override' || linkBehavior === 'none') return;\n            }\n            if (manager.selectionMode === 'single') {\n                if (manager.isSelected(key) && !manager.disallowEmptySelection) manager.toggleSelection(key);\n                else manager.replaceSelection(key);\n            } else if (e && e.shiftKey) manager.extendSelection(key);\n            else if (manager.selectionBehavior === 'toggle' || e && ((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__.isCtrlKeyPressed)(e) || e.pointerType === 'touch' || e.pointerType === 'virtual')) // if touch or virtual (VO) then we just want to toggle, otherwise it's impossible to multi select because they don't have modifier keys\n            manager.toggleSelection(key);\n            else manager.replaceSelection(key);\n        }\n    };\n    // Focus the associated DOM node when this item becomes the focusedKey\n    // TODO: can't make this useLayoutEffect bacause it breaks menus inside dialogs\n    // However, if this is a useEffect, it runs twice and dispatches two UPDATE_ACTIVEDESCENDANT and immediately sets\n    // aria-activeDescendant in useAutocomplete... I've worked around this for now\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        let isFocused = key === manager.focusedKey;\n        if (isFocused && manager.isFocused) {\n            if (!shouldUseVirtualFocus) {\n                if (focus) focus();\n                else if (document.activeElement !== ref.current && ref.current) (0, _react_aria_focus__WEBPACK_IMPORTED_MODULE_5__.focusSafely)(ref.current);\n            } else {\n                var _ref_current;\n                let updateActiveDescendant = new CustomEvent((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_6__.UPDATE_ACTIVEDESCENDANT), {\n                    cancelable: true,\n                    bubbles: true\n                });\n                (_ref_current = ref.current) === null || _ref_current === void 0 ? void 0 : _ref_current.dispatchEvent(updateActiveDescendant);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        ref,\n        key,\n        manager.focusedKey,\n        manager.childFocusStrategy,\n        manager.isFocused,\n        shouldUseVirtualFocus\n    ]);\n    isDisabled = isDisabled || manager.isDisabled(key);\n    // Set tabIndex to 0 if the element is focused, or -1 otherwise so that only the last focused\n    // item is tabbable.  If using virtual focus, don't set a tabIndex at all so that VoiceOver\n    // on iOS 14 doesn't try to move real DOM focus to the item anyway.\n    let itemProps = {};\n    if (!shouldUseVirtualFocus && !isDisabled) itemProps = {\n        tabIndex: key === manager.focusedKey ? 0 : -1,\n        onFocus (e) {\n            if (e.target === ref.current) manager.setFocusedKey(key);\n        }\n    };\n    else if (isDisabled) itemProps.onMouseDown = (e)=>{\n        // Prevent focus going to the body when clicking on a disabled item.\n        e.preventDefault();\n    };\n    // With checkbox selection, onAction (i.e. navigation) becomes primary, and occurs on a single click of the row.\n    // Clicking the checkbox enters selection mode, after which clicking anywhere on any row toggles selection for that row.\n    // With highlight selection, onAction is secondary, and occurs on double click. Single click selects the row.\n    // With touch, onAction occurs on single tap, and long press enters selection mode.\n    let isLinkOverride = manager.isLink(key) && linkBehavior === 'override';\n    let hasLinkAction = manager.isLink(key) && linkBehavior !== 'selection' && linkBehavior !== 'none';\n    let allowsSelection = !isDisabled && manager.canSelectItem(key) && !isLinkOverride;\n    let allowsActions = (onAction || hasLinkAction) && !isDisabled;\n    let hasPrimaryAction = allowsActions && (manager.selectionBehavior === 'replace' ? !allowsSelection : !allowsSelection || manager.isEmpty);\n    let hasSecondaryAction = allowsActions && allowsSelection && manager.selectionBehavior === 'replace';\n    let hasAction = hasPrimaryAction || hasSecondaryAction;\n    let modality = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    let longPressEnabled = hasAction && allowsSelection;\n    let longPressEnabledOnPressStart = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    let hadPrimaryActionOnPressStart = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    let performAction = (e)=>{\n        if (onAction) onAction();\n        if (hasLinkAction && ref.current) {\n            let itemProps = manager.getItemProps(key);\n            router.open(ref.current, e, itemProps.href, itemProps.routerOptions);\n        }\n    };\n    // By default, selection occurs on pointer down. This can be strange if selecting an\n    // item causes the UI to disappear immediately (e.g. menus).\n    // If shouldSelectOnPressUp is true, we use onPressUp instead of onPressStart.\n    // onPress requires a pointer down event on the same element as pointer up. For menus,\n    // we want to be able to have the pointer down on the trigger that opens the menu and\n    // the pointer up on the menu item rather than requiring a separate press.\n    // For keyboard events, selection still occurs on key down.\n    let itemPressProps = {};\n    if (shouldSelectOnPressUp) {\n        itemPressProps.onPressStart = (e)=>{\n            modality.current = e.pointerType;\n            longPressEnabledOnPressStart.current = longPressEnabled;\n            if (e.pointerType === 'keyboard' && (!hasAction || $880e95eb8b93ba9a$var$isSelectionKey())) onSelect(e);\n        };\n        // If allowsDifferentPressOrigin and interacting with mouse, make selection happen on pressUp (e.g. open menu on press down, selection on menu item happens on press up.)\n        // Otherwise, have selection happen onPress (prevents listview row selection when clicking on interactable elements in the row)\n        if (!allowsDifferentPressOrigin) itemPressProps.onPress = (e)=>{\n            if (hasPrimaryAction || hasSecondaryAction && e.pointerType !== 'mouse') {\n                if (e.pointerType === 'keyboard' && !$880e95eb8b93ba9a$var$isActionKey()) return;\n                performAction(e);\n            } else if (e.pointerType !== 'keyboard' && allowsSelection) onSelect(e);\n        };\n        else {\n            itemPressProps.onPressUp = hasPrimaryAction ? undefined : (e)=>{\n                if (e.pointerType === 'mouse' && allowsSelection) onSelect(e);\n            };\n            itemPressProps.onPress = hasPrimaryAction ? performAction : (e)=>{\n                if (e.pointerType !== 'keyboard' && e.pointerType !== 'mouse' && allowsSelection) onSelect(e);\n            };\n        }\n    } else {\n        itemPressProps.onPressStart = (e)=>{\n            modality.current = e.pointerType;\n            longPressEnabledOnPressStart.current = longPressEnabled;\n            hadPrimaryActionOnPressStart.current = hasPrimaryAction;\n            // Select on mouse down unless there is a primary action which will occur on mouse up.\n            // For keyboard, select on key down. If there is an action, the Space key selects on key down,\n            // and the Enter key performs onAction on key up.\n            if (allowsSelection && (e.pointerType === 'mouse' && !hasPrimaryAction || e.pointerType === 'keyboard' && (!allowsActions || $880e95eb8b93ba9a$var$isSelectionKey()))) onSelect(e);\n        };\n        itemPressProps.onPress = (e)=>{\n            // Selection occurs on touch up. Primary actions always occur on pointer up.\n            // Both primary and secondary actions occur on Enter key up. The only exception\n            // is secondary actions, which occur on double click with a mouse.\n            if (e.pointerType === 'touch' || e.pointerType === 'pen' || e.pointerType === 'virtual' || e.pointerType === 'keyboard' && hasAction && $880e95eb8b93ba9a$var$isActionKey() || e.pointerType === 'mouse' && hadPrimaryActionOnPressStart.current) {\n                if (hasAction) performAction(e);\n                else if (allowsSelection) onSelect(e);\n            }\n        };\n    }\n    itemProps['data-key'] = key;\n    itemPressProps.preventFocusOnPress = shouldUseVirtualFocus;\n    let { pressProps: pressProps, isPressed: isPressed } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_7__.usePress)(itemPressProps);\n    // Double clicking with a mouse with selectionBehavior = 'replace' performs an action.\n    let onDoubleClick = hasSecondaryAction ? (e)=>{\n        if (modality.current === 'mouse') {\n            e.stopPropagation();\n            e.preventDefault();\n            performAction(e);\n        }\n    } : undefined;\n    // Long pressing an item with touch when selectionBehavior = 'replace' switches the selection behavior\n    // to 'toggle'. This changes the single tap behavior from performing an action (i.e. navigating) to\n    // selecting, and may toggle the appearance of a UI affordance like checkboxes on each item.\n    let { longPressProps: longPressProps } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_8__.useLongPress)({\n        isDisabled: !longPressEnabled,\n        onLongPress (e) {\n            if (e.pointerType === 'touch') {\n                onSelect(e);\n                manager.setSelectionBehavior('toggle');\n            }\n        }\n    });\n    // Prevent native drag and drop on long press if we also select on long press.\n    // Once the user is in selection mode, they can long press again to drag.\n    // Use a capturing listener to ensure this runs before useDrag, regardless of\n    // the order the props get merged.\n    let onDragStartCapture = (e)=>{\n        if (modality.current === 'touch' && longPressEnabledOnPressStart.current) e.preventDefault();\n    };\n    // Prevent default on link clicks so that we control exactly\n    // when they open (to match selection behavior).\n    let onClick = manager.isLink(key) ? (e)=>{\n        if (!(0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.openLink).isOpening) e.preventDefault();\n    } : undefined;\n    return {\n        itemProps: (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_9__.mergeProps)(itemProps, allowsSelection || hasPrimaryAction ? pressProps : {}, longPressEnabled ? longPressProps : {}, {\n            onDoubleClick: onDoubleClick,\n            onDragStartCapture: onDragStartCapture,\n            onClick: onClick,\n            id: id\n        }),\n        isPressed: isPressed,\n        isSelected: manager.isSelected(key),\n        isFocused: manager.isFocused && manager.focusedKey === key,\n        isDisabled: isDisabled,\n        allowsSelection: allowsSelection,\n        hasAction: hasAction\n    };\n}\nfunction $880e95eb8b93ba9a$var$isActionKey() {\n    let event = window.event;\n    return (event === null || event === void 0 ? void 0 : event.key) === 'Enter';\n}\nfunction $880e95eb8b93ba9a$var$isSelectionKey() {\n    let event = window.event;\n    return (event === null || event === void 0 ? void 0 : event.key) === ' ' || (event === null || event === void 0 ? void 0 : event.code) === 'Space';\n}\n\n\n\n//# sourceMappingURL=useSelectableItem.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/useSelectableItem.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/useSelectableList.mjs":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/useSelectableList.mjs ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSelectableList: () => (/* binding */ $982254629710d113$export$b95089534ab7c1fd)\n/* harmony export */ });\n/* harmony import */ var _useSelectableCollection_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useSelectableCollection.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/useSelectableCollection.mjs\");\n/* harmony import */ var _ListKeyboardDelegate_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ListKeyboardDelegate.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/ListKeyboardDelegate.mjs\");\n/* harmony import */ var _react_aria_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/i18n */ \"(ssr)/./node_modules/.pnpm/@react-aria+i18n@3.12.5_rea_c3394d9e3bca4b1a38969e4ba9336700/node_modules/@react-aria/i18n/dist/useCollator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n\nfunction $982254629710d113$export$b95089534ab7c1fd(props) {\n    let { selectionManager: selectionManager, collection: collection, disabledKeys: disabledKeys, ref: ref, keyboardDelegate: keyboardDelegate, layoutDelegate: layoutDelegate } = props;\n    // By default, a KeyboardDelegate is provided which uses the DOM to query layout information (e.g. for page up/page down).\n    // When virtualized, the layout object will be passed in as a prop and override this.\n    let collator = (0, _react_aria_i18n__WEBPACK_IMPORTED_MODULE_1__.useCollator)({\n        usage: 'search',\n        sensitivity: 'base'\n    });\n    let disabledBehavior = selectionManager.disabledBehavior;\n    let delegate = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>keyboardDelegate || new (0, _ListKeyboardDelegate_mjs__WEBPACK_IMPORTED_MODULE_2__.ListKeyboardDelegate)({\n            collection: collection,\n            disabledKeys: disabledKeys,\n            disabledBehavior: disabledBehavior,\n            ref: ref,\n            collator: collator,\n            layoutDelegate: layoutDelegate\n        }), [\n        keyboardDelegate,\n        layoutDelegate,\n        collection,\n        disabledKeys,\n        ref,\n        collator,\n        disabledBehavior\n    ]);\n    let { collectionProps: collectionProps } = (0, _useSelectableCollection_mjs__WEBPACK_IMPORTED_MODULE_3__.useSelectableCollection)({\n        ...props,\n        ref: ref,\n        selectionManager: selectionManager,\n        keyboardDelegate: delegate\n    });\n    return {\n        listProps: collectionProps\n    };\n}\n\n\n\n//# sourceMappingURL=useSelectableList.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/useSelectableList.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/useTypeSelect.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/useTypeSelect.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTypeSelect: () => (/* binding */ $fb3050f43d946246$export$e32c88dfddc6e1d8)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n/**\n * Controls how long to wait before clearing the typeahead buffer.\n */ const $fb3050f43d946246$var$TYPEAHEAD_DEBOUNCE_WAIT_MS = 1000; // 1 second\nfunction $fb3050f43d946246$export$e32c88dfddc6e1d8(options) {\n    let { keyboardDelegate: keyboardDelegate, selectionManager: selectionManager, onTypeSelect: onTypeSelect } = options;\n    let state = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        search: '',\n        timeout: undefined\n    }).current;\n    let onKeyDown = (e)=>{\n        let character = $fb3050f43d946246$var$getStringForKey(e.key);\n        if (!character || e.ctrlKey || e.metaKey || !e.currentTarget.contains(e.target)) return;\n        // Do not propagate the Spacebar event if it's meant to be part of the search.\n        // When we time out, the search term becomes empty, hence the check on length.\n        // Trimming is to account for the case of pressing the Spacebar more than once,\n        // which should cycle through the selection/deselection of the focused item.\n        if (character === ' ' && state.search.trim().length > 0) {\n            e.preventDefault();\n            if (!('continuePropagation' in e)) e.stopPropagation();\n        }\n        state.search += character;\n        if (keyboardDelegate.getKeyForSearch != null) {\n            // Use the delegate to find a key to focus.\n            // Prioritize items after the currently focused item, falling back to searching the whole list.\n            let key = keyboardDelegate.getKeyForSearch(state.search, selectionManager.focusedKey);\n            // If no key found, search from the top.\n            if (key == null) key = keyboardDelegate.getKeyForSearch(state.search);\n            if (key != null) {\n                selectionManager.setFocusedKey(key);\n                if (onTypeSelect) onTypeSelect(key);\n            }\n        }\n        clearTimeout(state.timeout);\n        state.timeout = setTimeout(()=>{\n            state.search = '';\n        }, $fb3050f43d946246$var$TYPEAHEAD_DEBOUNCE_WAIT_MS);\n    };\n    return {\n        typeSelectProps: {\n            // Using a capturing listener to catch the keydown event before\n            // other hooks in order to handle the Spacebar event.\n            onKeyDownCapture: keyboardDelegate.getKeyForSearch ? onKeyDown : undefined\n        }\n    };\n}\nfunction $fb3050f43d946246$var$getStringForKey(key) {\n    // If the key is of length 1, it is an ASCII value.\n    // Otherwise, if there are no ASCII characters in the key name,\n    // it is a Unicode character.\n    // See https://www.w3.org/TR/uievents-key/\n    if (key.length === 1 || !/^[A-Z]/i.test(key)) return key;\n    return '';\n}\n\n\n\n//# sourceMappingURL=useTypeSelect.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/useTypeSelect.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/utils.mjs":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/utils.mjs ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isNonContiguousSelectionModifier: () => (/* binding */ $feb5ffebff200149$export$d3e3bd3e26688c04)\n/* harmony export */ });\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/platform.mjs\");\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \nfunction $feb5ffebff200149$export$d3e3bd3e26688c04(e) {\n    // Ctrl + Arrow Up/Arrow Down has a system wide meaning on macOS, so use Alt instead.\n    // On Windows and Ubuntu, Alt + Space has a system wide meaning.\n    return (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.isAppleDevice)() ? e.altKey : e.ctrlKey;\n}\n\n\n\n//# sourceMappingURL=utils.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+selection@3.22._9239b7f0435a0b7caa7017c26caf33ba/node_modules/@react-aria/selection/dist/utils.mjs\n");

/***/ })

};
;