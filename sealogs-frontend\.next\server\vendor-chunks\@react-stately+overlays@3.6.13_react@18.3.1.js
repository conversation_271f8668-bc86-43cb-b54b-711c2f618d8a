"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-stately+overlays@3.6.13_react@18.3.1";
exports.ids = ["vendor-chunks/@react-stately+overlays@3.6.13_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+overlays@3.6.13_react@18.3.1/node_modules/@react-stately/overlays/dist/useOverlayTriggerState.mjs":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+overlays@3.6.13_react@18.3.1/node_modules/@react-stately/overlays/dist/useOverlayTriggerState.mjs ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOverlayTriggerState: () => (/* binding */ $fc909762b330b746$export$61c6a8c84e605fb6)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_stately_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-stately/utils */ \"(ssr)/./node_modules/.pnpm/@react-stately+utils@3.10.5_react@18.3.1/node_modules/@react-stately/utils/dist/useControlledState.mjs\");\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\nfunction $fc909762b330b746$export$61c6a8c84e605fb6(props) {\n    let [isOpen, setOpen] = (0, _react_stately_utils__WEBPACK_IMPORTED_MODULE_1__.useControlledState)(props.isOpen, props.defaultOpen || false, props.onOpenChange);\n    const open = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setOpen(true);\n    }, [\n        setOpen\n    ]);\n    const close = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setOpen(false);\n    }, [\n        setOpen\n    ]);\n    const toggle = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setOpen(!isOpen);\n    }, [\n        setOpen,\n        isOpen\n    ]);\n    return {\n        isOpen: isOpen,\n        setOpen: setOpen,\n        open: open,\n        close: close,\n        toggle: toggle\n    };\n}\n\n\n\n//# sourceMappingURL=useOverlayTriggerState.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+overlays@3.6.13_react@18.3.1/node_modules/@react-stately/overlays/dist/useOverlayTriggerState.mjs\n");

/***/ })

};
;