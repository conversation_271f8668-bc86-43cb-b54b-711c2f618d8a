"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/new/page",{

/***/ "(app-pages-browser)/./src/app/ui/inventory/inventory-new.tsx":
/*!************************************************!*\
  !*** ./src/app/ui/inventory/inventory-new.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewInventory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var react_aria_components__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! react-aria-components */ \"(app-pages-browser)/./node_modules/.pnpm/react-aria-components@1.6.0_c73dd79e59f59972f050dd4780f2df6c/node_modules/react-aria-components/dist/Heading.mjs\");\n/* harmony import */ var _app_ui_editor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/ui/editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _components_file_upload__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/file-upload */ \"(app-pages-browser)/./src/components/file-upload.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Hash,Package,Paperclip,ShoppingBag,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Hash,Package,Paperclip,ShoppingBag,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Hash,Package,Paperclip,ShoppingBag,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Hash,Package,Paperclip,ShoppingBag,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Hash,Package,Paperclip,ShoppingBag,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Hash,Package,Paperclip,ShoppingBag,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Hash,Package,Paperclip,ShoppingBag,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_ui_sea_logs_button__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/sea-logs-button */ \"(app-pages-browser)/./src/components/ui/sea-logs-button.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewInventory(param) {\n    let { vesselID = 0 } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedCategories, setSelectedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [suppliers, setSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedSuppliers, setSelectedSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [location, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedVessel, setSelectedVessel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [openLocationDialog, setOpenLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openSupplierDialog, setOpenSupplierDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openCategoryDialog, setOpenCategoryDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    var description = \"\";\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const vesselList = activeVessels.map((item)=>({\n                ...item\n            }));\n        const appendedData = [\n            // { title: '-- Other --', id: 'newLocation' },\n            ...vesselList,\n            {\n                title: \"Other\",\n                id: \"0\"\n            }\n        ];\n        setVessels(appendedData);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getVesselList)(handleSetVessels);\n    const handelSetSuppliers = (data)=>{\n        const suppliersList = [\n            {\n                label: \" ---- Create Supplier ---- \",\n                value: \"newSupplier\"\n            },\n            ...data === null || data === void 0 ? void 0 : data.filter((supplier)=>supplier.name !== null).map((supplier)=>({\n                    label: supplier.name,\n                    value: supplier.id\n                }))\n        ];\n        setSuppliers(suppliersList);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getSupplier)(handelSetSuppliers);\n    const handleSetCategories = (data)=>{\n        const categoriesList = [\n            {\n                label: \" ---- Create Category ---- \",\n                value: \"newCategory\"\n            },\n            ...data === null || data === void 0 ? void 0 : data.filter((category)=>category.name !== null).map((category)=>({\n                    label: category.name,\n                    value: category.id\n                }))\n        ];\n        setCategories(categoriesList);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getInventoryCategory)(handleSetCategories);\n    const handleSetSelectedCategories = (selectedOption)=>{\n        if (selectedOption.find((option)=>option.value === \"newCategory\")) {\n            setOpenCategoryDialog(true);\n        }\n        setSelectedCategories(selectedOption.filter((option)=>option.value !== \"newCategory\"));\n    };\n    const handleEditorChange = (desc)=>{\n        description = desc;\n    };\n    const handleCreate = async ()=>{\n        const variables = {\n            input: {\n                item: document.getElementById(\"inventory-name\").value ? document.getElementById(\"inventory-name\").value : null,\n                description: document.getElementById(\"inventory-short-description\").value ? document.getElementById(\"inventory-short-description\").value : null,\n                content: description,\n                quantity: document.getElementById(\"inventory-qty\").value ? parseInt(document.getElementById(\"inventory-qty\").value) : null,\n                productCode: document.getElementById(\"inventory-code\").value ? document.getElementById(\"inventory-code\").value : null,\n                costingDetails: document.getElementById(\"inventory-cost\").value ? document.getElementById(\"inventory-cost\").value : null,\n                documents: documents.map((doc)=>doc.id).join(\",\"),\n                categories: (selectedCategories === null || selectedCategories === void 0 ? void 0 : selectedCategories.map((category)=>category.value).length) ? selectedCategories.map((category)=>category.value).join(\",\") : null,\n                suppliers: (selectedSuppliers === null || selectedSuppliers === void 0 ? void 0 : selectedSuppliers.map((supplier)=>supplier.value).length) ? selectedSuppliers.map((supplier)=>supplier.value).join(\",\") : null,\n                location: document.getElementById(\"inventory-location\").value ? document.getElementById(\"inventory-location\").value : null,\n                vesselID: vesselID > 0 ? vesselID : selectedLocation === null || selectedLocation === void 0 ? void 0 : selectedLocation.value\n            }\n        };\n        await mutationCreateInventory({\n            variables\n        });\n    };\n    const [mutationCreateInventory, { loading: mutationcreateInventoryLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_INVENTORY, {\n        onCompleted: (response)=>{\n            const data = response.createInventory;\n            if (data.id > 0) {\n                searchParams.get(\"redirect_to\") ? router.push((searchParams === null || searchParams === void 0 ? void 0 : searchParams.get(\"redirect_to\")) + \"\") : router.back();\n            } else {\n                console.error(\"mutationcreateInventory error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationcreateInventory error\", error);\n        }\n    });\n    const handleCreateCategory = async ()=>{\n        const categoryName = document.getElementById(\"inventory-new-category\").value;\n        return await mutationcreateInventoryCategory({\n            variables: {\n                input: {\n                    name: categoryName\n                }\n            }\n        });\n    };\n    const [mutationcreateInventoryCategory, { loading: mutationcreateInventoryCategoryLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_INVENTORY_CATEGORY, {\n        onCompleted: (response)=>{\n            const data = response.createInventoryCategory;\n            if (data.id > 0) {\n                const categoriesList = [\n                    ...categories,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setCategories(categoriesList);\n                const selectedCategoriesList = [\n                    ...selectedCategories,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSelectedCategories(selectedCategoriesList);\n                setOpenCategoryDialog(false);\n            } else {\n                console.error(\"mutationcreateInventoryCategory error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationcreateInventoryCategory error\", error);\n        }\n    });\n    const handleSelectedVesselChange = (selectedOption)=>{\n        if (selectedOption.value === \"newLocation\") {\n            setOpenLocationDialog(true);\n        }\n        setSelectedLocation(selectedOption);\n    };\n    const handleCreateLocation = (Location)=>{\n        var newLocation = {\n            label: \"\",\n            value: \"\"\n        };\n        if (typeof Location === \"string\") {\n            newLocation = {\n                label: Location,\n                value: Location\n            };\n        }\n        if (typeof Location === \"object\") {\n            newLocation = {\n                label: document.getElementById(\"inventory-new-location\").value,\n                value: document.getElementById(\"inventory-new-location-id\").value ? document.getElementById(\"inventory-new-location-id\").value : document.getElementById(\"inventory-new-location\").value\n            };\n        }\n        const vesselList = vessels.map((item)=>({\n                ...item\n            }));\n        const appendedData = [\n            ...vesselList,\n            {\n                Title: newLocation.label,\n                ID: newLocation.value\n            }\n        ];\n        setVessels(appendedData);\n        setSelectedLocation(newLocation);\n        setOpenLocationDialog(false);\n    };\n    const handleSelectedSuppliers = (selectedOption)=>{\n        if (selectedOption.find((option)=>option.value === \"newSupplier\")) {\n            setOpenSupplierDialog(true);\n        }\n        setSelectedSuppliers(selectedOption.filter((option)=>option.value !== \"newSupplier\"));\n    };\n    const handleCreateSupplier = async ()=>{\n        const name = document.getElementById(\"supplier-name\").value;\n        const website = document.getElementById(\"supplier-website\").value;\n        const phone = document.getElementById(\"supplier-phone\").value;\n        const email = document.getElementById(\"supplier-email\").value;\n        const address = document.getElementById(\"supplier-address\").value;\n        const variables = {\n            input: {\n                name: name,\n                address: address,\n                website: website,\n                email: email,\n                phone: phone\n            }\n        };\n        if (name !== \"\") {\n            await mutationCreateSupplier({\n                variables\n            });\n        }\n        setOpenSupplierDialog(false);\n    };\n    const [mutationCreateSupplier, { loading: mutationcreateSupplierLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_SUPPLIER, {\n        onCompleted: (response)=>{\n            const data = response.createSupplier;\n            if (data.id > 0) {\n                const suppliersList = [\n                    ...suppliers,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSuppliers(suppliersList);\n                const selectedSuppliersList = [\n                    ...selectedSuppliers,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSelectedSuppliers(selectedSuppliersList);\n            } else {\n                console.error(\"mutationcreateSupplier error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationcreateSupplier error\", error);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.CardHeader, {\n                className: \"border-b pb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Ca, {\n                    className: \"text-2xl font-medium flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"h-6 w-6 text-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 21\n                        }, this),\n                        \"New Inventory\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 401,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        htmlFor: \"inventory-name\",\n                                        className: \"text-sm font-medium\",\n                                        children: \"Inventory Name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                        id: \"inventory-name\",\n                                        type: \"text\",\n                                        placeholder: \"Inventory name\",\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        htmlFor: \"inventory-vessel\",\n                                        className: \"text-sm font-medium\",\n                                        children: \"Vessel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 25\n                                    }, this),\n                                    vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                                        id: \"inventory-vessel\",\n                                        options: vessels === null || vessels === void 0 ? void 0 : vessels.map((vessel)=>({\n                                                label: vessel.title,\n                                                value: vessel.id\n                                            })),\n                                        defaultValues: vesselID > 0 && vessels.filter((vessel)=>vessel.id === vesselID).map((vessel)=>({\n                                                label: vessel.title,\n                                                value: vessel.id\n                                            })),\n                                        placeholder: \"Select Vessel \".concat(vesselID),\n                                        onChange: handleSelectedVesselChange\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 29\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_6__.InputSkeleton, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        htmlFor: \"inventory-location\",\n                                        className: \"text-sm font-medium\",\n                                        children: \"Location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                        id: \"inventory-location\",\n                                        type: \"text\",\n                                        placeholder: \"Location\",\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        htmlFor: \"inventory-qty\",\n                                        className: \"text-sm font-medium\",\n                                        children: \"Quantity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                        id: \"inventory-qty\",\n                                        type: \"number\",\n                                        placeholder: \"Quantity\",\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                htmlFor: \"inventory-short-description\",\n                                className: \"text-sm font-medium flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 25\n                                    }, this),\n                                    \"Short Description\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                id: \"inventory-short-description\",\n                                rows: 12,\n                                className: \"w-full resize-none\",\n                                placeholder: \"Short description\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 409,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 503,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 25\n                                    }, this),\n                                    \"Inventory Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-sm leading-relaxed\",\n                                children: \"In this section categorise the item and add the suppliers where you normally purchase this item and the expected cost. This will help replacing the item in the future.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2 space-y-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        htmlFor: \"inventory-code\",\n                                        className: \"text-sm font-medium flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 29\n                                            }, this),\n                                            \"Product code\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                        id: \"inventory-code\",\n                                        type: \"text\",\n                                        placeholder: \"Product code\",\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        htmlFor: \"inventory-categories\",\n                                        className: \"text-sm font-medium flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 29\n                                            }, this),\n                                            \"Categories\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 25\n                                    }, this),\n                                    categories ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                                        id: \"inventory-categories\",\n                                        multi: true,\n                                        options: categories,\n                                        value: selectedCategories,\n                                        onChange: handleSetSelectedCategories\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 29\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_6__.InputSkeleton, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        htmlFor: \"inventory-suppliers\",\n                                        className: \"text-sm font-medium flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 29\n                                            }, this),\n                                            \"Supplier\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 25\n                                    }, this),\n                                    suppliers ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                                        id: \"inventory-suppliers\",\n                                        multi: true,\n                                        value: selectedSuppliers,\n                                        onChange: handleSelectedSuppliers,\n                                        options: suppliers\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 29\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_6__.InputSkeleton, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        htmlFor: \"inventory-cost\",\n                                        className: \"text-sm font-medium flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 29\n                                            }, this),\n                                            \"Cost\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                        id: \"inventory-cost\",\n                                        type: \"text\",\n                                        placeholder: \"Costing Details\",\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 506,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 593,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 25\n                                    }, this),\n                                    \"Attachment\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-sm leading-relaxed\",\n                                children: \"Upload things like photos of the item, plus warranty and guarantee documents or operating manuals. Add links to any online manuals or product descriptions.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 602,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 597,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2 space-y-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_file_upload__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    setDocuments: setDocuments,\n                                    text: \"\",\n                                    subText: \"Drag files here or upload\",\n                                    bgClass: \"bg-muted/30 border-2 border-dashed border-muted-foreground/20 rounded-lg p-6\",\n                                    documents: documents\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 610,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        htmlFor: \"inventory-links\",\n                                        className: \"text-sm font-medium\",\n                                        children: \"Links\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                        id: \"inventory-links\",\n                                        type: \"text\",\n                                        placeholder: \"Links to manuals or product descriptions\",\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 609,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 596,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 636,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 25\n                                    }, this),\n                                    \"Description\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 641,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-sm leading-relaxed\",\n                                children: \"Enter details that might help with the maintenance or operation of this item.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            id: \"inventory-Content\",\n                            handleEditorChange: handleEditorChange\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 651,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 639,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_13__.FooterWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sea_logs_button__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        action: ()=>router.back(),\n                        type: \"text\",\n                        text: \"Cancel\",\n                        className: \"hover:bg-muted\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sea_logs_button__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        color: \"sky\",\n                        action: handleCreate,\n                        icon: \"check\",\n                        type: \"primary\",\n                        text: \"Create Inventory\",\n                        className: \"bg-primary text-primary-foreground hover:bg-primary/90\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 666,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 659,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.AlertDialogNew, {\n                openDialog: openLocationDialog,\n                setOpenDialog: setOpenLocationDialog,\n                handleCreate: ()=>handleCreateLocation({}),\n                actionText: \"Create Location\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_aria_components__WEBPACK_IMPORTED_MODULE_24__.Heading, {\n                        slot: \"title\",\n                        className: \"text-2xl  leading-6 my-2 \",\n                        children: \"Create New Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 682,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                            id: \"inventory-new-location\",\n                            type: \"text\",\n                            placeholder: \"Location\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                            lineNumber: 686,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 685,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                            id: \"inventory-new-location-id\",\n                            type: \"text\",\n                            placeholder: \"Location ID\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                            lineNumber: 693,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 692,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 677,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.AlertDialogNew, {\n                openDialog: openSupplierDialog,\n                setOpenDialog: setOpenSupplierDialog,\n                handleCreate: handleCreateSupplier,\n                actionText: \"Create Supplier\",\n                className: \"lg:max-w-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_aria_components__WEBPACK_IMPORTED_MODULE_24__.Heading, {\n                        slot: \"title\",\n                        className: \"text-2xl  leading-6 my-2 \",\n                        children: \"Create New Supplier\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 707,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                    id: \"supplier-name\",\n                                    type: \"text\",\n                                    placeholder: \"Supplier name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 711,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                    id: \"supplier-website\",\n                                    type: \"text\",\n                                    placeholder: \"Website\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 718,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                    id: \"supplier-phone\",\n                                    type: \"text\",\n                                    placeholder: \"Phone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 725,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                    id: \"supplier-email\",\n                                    type: \"email\",\n                                    placeholder: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 733,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                    id: \"supplier-address\",\n                                    rows: 4,\n                                    className: \" p-2\",\n                                    placeholder: \"Supplier address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 740,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 739,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 710,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 701,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.AlertDialogNew, {\n                openDialog: openCategoryDialog,\n                setOpenDialog: setOpenCategoryDialog,\n                handleCreate: handleCreateCategory,\n                actionText: \"Create Category\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_aria_components__WEBPACK_IMPORTED_MODULE_24__.Heading, {\n                        slot: \"title\",\n                        className: \"text-2xl  leading-6 my-2 \",\n                        children: \"Create New Category\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 755,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                            id: \"inventory-new-category\",\n                            type: \"text\",\n                            placeholder: \"Category\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                            lineNumber: 759,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 758,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 750,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n        lineNumber: 399,\n        columnNumber: 9\n    }, this);\n}\n_s(NewInventory, \"oNVcfeakna0lpaiWO9Usb234muo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation\n    ];\n});\n_c = NewInventory;\nvar _c;\n$RefreshReg$(_c, \"NewInventory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/inventory/inventory-new.tsx\n"));

/***/ })

});