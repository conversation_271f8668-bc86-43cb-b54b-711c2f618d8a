"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+separator@3.4.5_b878ef3a27ecedb368613f2e0ef98f74";
exports.ids = ["vendor-chunks/@react-aria+separator@3.4.5_b878ef3a27ecedb368613f2e0ef98f74"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+separator@3.4.5_b878ef3a27ecedb368613f2e0ef98f74/node_modules/@react-aria/separator/dist/useSeparator.mjs":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+separator@3.4.5_b878ef3a27ecedb368613f2e0ef98f74/node_modules/@react-aria/separator/dist/useSeparator.mjs ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSeparator: () => (/* binding */ $f4b273590fab9f93$export$52210f68a14655d0)\n/* harmony export */ });\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/filterDOMProps.mjs\");\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \nfunction $f4b273590fab9f93$export$52210f68a14655d0(props) {\n    let domProps = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.filterDOMProps)(props, {\n        labelable: true\n    });\n    let ariaOrientation;\n    // if orientation is horizontal, aria-orientation default is horizontal, so we leave it undefined\n    // if it's vertical, we need to specify it\n    if (props.orientation === 'vertical') ariaOrientation = 'vertical';\n    // hr elements implicitly have role = separator and a horizontal orientation\n    if (props.elementType !== 'hr') return {\n        separatorProps: {\n            ...domProps,\n            role: 'separator',\n            'aria-orientation': ariaOrientation\n        }\n    };\n    return {\n        separatorProps: domProps\n    };\n}\n\n\n\n//# sourceMappingURL=useSeparator.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+separator@3.4.5_b878ef3a27ecedb368613f2e0ef98f74/node_modules/@react-aria/separator/dist/useSeparator.mjs\n");

/***/ })

};
;