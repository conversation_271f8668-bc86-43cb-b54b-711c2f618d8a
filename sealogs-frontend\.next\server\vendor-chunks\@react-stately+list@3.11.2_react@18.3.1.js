"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-stately+list@3.11.2_react@18.3.1";
exports.ids = ["vendor-chunks/@react-stately+list@3.11.2_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+list@3.11.2_react@18.3.1/node_modules/@react-stately/list/dist/ListCollection.mjs":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+list@3.11.2_react@18.3.1/node_modules/@react-stately/list/dist/ListCollection.mjs ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListCollection: () => (/* binding */ $a02d57049d202695$export$d085fb9e920b5ca7)\n/* harmony export */ });\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ class $a02d57049d202695$export$d085fb9e920b5ca7 {\n    *[Symbol.iterator]() {\n        yield* this.iterable;\n    }\n    get size() {\n        return this.keyMap.size;\n    }\n    getKeys() {\n        return this.keyMap.keys();\n    }\n    getKeyBefore(key) {\n        let node = this.keyMap.get(key);\n        var _node_prevKey;\n        return node ? (_node_prevKey = node.prevKey) !== null && _node_prevKey !== void 0 ? _node_prevKey : null : null;\n    }\n    getKeyAfter(key) {\n        let node = this.keyMap.get(key);\n        var _node_nextKey;\n        return node ? (_node_nextKey = node.nextKey) !== null && _node_nextKey !== void 0 ? _node_nextKey : null : null;\n    }\n    getFirstKey() {\n        return this.firstKey;\n    }\n    getLastKey() {\n        return this.lastKey;\n    }\n    getItem(key) {\n        var _this_keyMap_get;\n        return (_this_keyMap_get = this.keyMap.get(key)) !== null && _this_keyMap_get !== void 0 ? _this_keyMap_get : null;\n    }\n    at(idx) {\n        const keys = [\n            ...this.getKeys()\n        ];\n        return this.getItem(keys[idx]);\n    }\n    getChildren(key) {\n        let node = this.keyMap.get(key);\n        return (node === null || node === void 0 ? void 0 : node.childNodes) || [];\n    }\n    constructor(nodes){\n        this.keyMap = new Map();\n        this.firstKey = null;\n        this.lastKey = null;\n        this.iterable = nodes;\n        let visit = (node)=>{\n            this.keyMap.set(node.key, node);\n            if (node.childNodes && node.type === 'section') for (let child of node.childNodes)visit(child);\n        };\n        for (let node of nodes)visit(node);\n        let last = null;\n        let index = 0;\n        for (let [key, node] of this.keyMap){\n            if (last) {\n                last.nextKey = key;\n                node.prevKey = last.key;\n            } else {\n                this.firstKey = key;\n                node.prevKey = undefined;\n            }\n            if (node.type === 'item') node.index = index++;\n            last = node;\n            // Set nextKey as undefined since this might be the last node\n            // If it isn't the last node, last.nextKey will properly set at start of new loop\n            last.nextKey = undefined;\n        }\n        var _last_key;\n        this.lastKey = (_last_key = last === null || last === void 0 ? void 0 : last.key) !== null && _last_key !== void 0 ? _last_key : null;\n    }\n}\n\n\n\n//# sourceMappingURL=ListCollection.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXN0YXRlbHkrbGlzdEAzLjExLjJfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9AcmVhY3Qtc3RhdGVseS9saXN0L2Rpc3QvTGlzdENvbGxlY3Rpb24ubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUdxRTtBQUNyRSIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXN0YXRlbHkrbGlzdEAzLjExLjJfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9AcmVhY3Qtc3RhdGVseS9saXN0L2Rpc3QvTGlzdENvbGxlY3Rpb24ubWpzP2VkZDUiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAqIENvcHlyaWdodCAyMDIwIEFkb2JlLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICogVGhpcyBmaWxlIGlzIGxpY2Vuc2VkIHRvIHlvdSB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLiBZb3UgbWF5IG9idGFpbiBhIGNvcHlcbiAqIG9mIHRoZSBMaWNlbnNlIGF0IGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmUgZGlzdHJpYnV0ZWQgdW5kZXJcbiAqIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUywgV0lUSE9VVCBXQVJSQU5USUVTIE9SIFJFUFJFU0VOVEFUSU9OU1xuICogT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlXG4gKiBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovIGNsYXNzICRhMDJkNTcwNDlkMjAyNjk1JGV4cG9ydCRkMDg1ZmI5ZTkyMGI1Y2E3IHtcbiAgICAqW1N5bWJvbC5pdGVyYXRvcl0oKSB7XG4gICAgICAgIHlpZWxkKiB0aGlzLml0ZXJhYmxlO1xuICAgIH1cbiAgICBnZXQgc2l6ZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMua2V5TWFwLnNpemU7XG4gICAgfVxuICAgIGdldEtleXMoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmtleU1hcC5rZXlzKCk7XG4gICAgfVxuICAgIGdldEtleUJlZm9yZShrZXkpIHtcbiAgICAgICAgbGV0IG5vZGUgPSB0aGlzLmtleU1hcC5nZXQoa2V5KTtcbiAgICAgICAgdmFyIF9ub2RlX3ByZXZLZXk7XG4gICAgICAgIHJldHVybiBub2RlID8gKF9ub2RlX3ByZXZLZXkgPSBub2RlLnByZXZLZXkpICE9PSBudWxsICYmIF9ub2RlX3ByZXZLZXkgIT09IHZvaWQgMCA/IF9ub2RlX3ByZXZLZXkgOiBudWxsIDogbnVsbDtcbiAgICB9XG4gICAgZ2V0S2V5QWZ0ZXIoa2V5KSB7XG4gICAgICAgIGxldCBub2RlID0gdGhpcy5rZXlNYXAuZ2V0KGtleSk7XG4gICAgICAgIHZhciBfbm9kZV9uZXh0S2V5O1xuICAgICAgICByZXR1cm4gbm9kZSA/IChfbm9kZV9uZXh0S2V5ID0gbm9kZS5uZXh0S2V5KSAhPT0gbnVsbCAmJiBfbm9kZV9uZXh0S2V5ICE9PSB2b2lkIDAgPyBfbm9kZV9uZXh0S2V5IDogbnVsbCA6IG51bGw7XG4gICAgfVxuICAgIGdldEZpcnN0S2V5KCkge1xuICAgICAgICByZXR1cm4gdGhpcy5maXJzdEtleTtcbiAgICB9XG4gICAgZ2V0TGFzdEtleSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubGFzdEtleTtcbiAgICB9XG4gICAgZ2V0SXRlbShrZXkpIHtcbiAgICAgICAgdmFyIF90aGlzX2tleU1hcF9nZXQ7XG4gICAgICAgIHJldHVybiAoX3RoaXNfa2V5TWFwX2dldCA9IHRoaXMua2V5TWFwLmdldChrZXkpKSAhPT0gbnVsbCAmJiBfdGhpc19rZXlNYXBfZ2V0ICE9PSB2b2lkIDAgPyBfdGhpc19rZXlNYXBfZ2V0IDogbnVsbDtcbiAgICB9XG4gICAgYXQoaWR4KSB7XG4gICAgICAgIGNvbnN0IGtleXMgPSBbXG4gICAgICAgICAgICAuLi50aGlzLmdldEtleXMoKVxuICAgICAgICBdO1xuICAgICAgICByZXR1cm4gdGhpcy5nZXRJdGVtKGtleXNbaWR4XSk7XG4gICAgfVxuICAgIGdldENoaWxkcmVuKGtleSkge1xuICAgICAgICBsZXQgbm9kZSA9IHRoaXMua2V5TWFwLmdldChrZXkpO1xuICAgICAgICByZXR1cm4gKG5vZGUgPT09IG51bGwgfHwgbm9kZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogbm9kZS5jaGlsZE5vZGVzKSB8fCBbXTtcbiAgICB9XG4gICAgY29uc3RydWN0b3Iobm9kZXMpe1xuICAgICAgICB0aGlzLmtleU1hcCA9IG5ldyBNYXAoKTtcbiAgICAgICAgdGhpcy5maXJzdEtleSA9IG51bGw7XG4gICAgICAgIHRoaXMubGFzdEtleSA9IG51bGw7XG4gICAgICAgIHRoaXMuaXRlcmFibGUgPSBub2RlcztcbiAgICAgICAgbGV0IHZpc2l0ID0gKG5vZGUpPT57XG4gICAgICAgICAgICB0aGlzLmtleU1hcC5zZXQobm9kZS5rZXksIG5vZGUpO1xuICAgICAgICAgICAgaWYgKG5vZGUuY2hpbGROb2RlcyAmJiBub2RlLnR5cGUgPT09ICdzZWN0aW9uJykgZm9yIChsZXQgY2hpbGQgb2Ygbm9kZS5jaGlsZE5vZGVzKXZpc2l0KGNoaWxkKTtcbiAgICAgICAgfTtcbiAgICAgICAgZm9yIChsZXQgbm9kZSBvZiBub2Rlcyl2aXNpdChub2RlKTtcbiAgICAgICAgbGV0IGxhc3QgPSBudWxsO1xuICAgICAgICBsZXQgaW5kZXggPSAwO1xuICAgICAgICBmb3IgKGxldCBba2V5LCBub2RlXSBvZiB0aGlzLmtleU1hcCl7XG4gICAgICAgICAgICBpZiAobGFzdCkge1xuICAgICAgICAgICAgICAgIGxhc3QubmV4dEtleSA9IGtleTtcbiAgICAgICAgICAgICAgICBub2RlLnByZXZLZXkgPSBsYXN0LmtleTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgdGhpcy5maXJzdEtleSA9IGtleTtcbiAgICAgICAgICAgICAgICBub2RlLnByZXZLZXkgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAobm9kZS50eXBlID09PSAnaXRlbScpIG5vZGUuaW5kZXggPSBpbmRleCsrO1xuICAgICAgICAgICAgbGFzdCA9IG5vZGU7XG4gICAgICAgICAgICAvLyBTZXQgbmV4dEtleSBhcyB1bmRlZmluZWQgc2luY2UgdGhpcyBtaWdodCBiZSB0aGUgbGFzdCBub2RlXG4gICAgICAgICAgICAvLyBJZiBpdCBpc24ndCB0aGUgbGFzdCBub2RlLCBsYXN0Lm5leHRLZXkgd2lsbCBwcm9wZXJseSBzZXQgYXQgc3RhcnQgb2YgbmV3IGxvb3BcbiAgICAgICAgICAgIGxhc3QubmV4dEtleSA9IHVuZGVmaW5lZDtcbiAgICAgICAgfVxuICAgICAgICB2YXIgX2xhc3Rfa2V5O1xuICAgICAgICB0aGlzLmxhc3RLZXkgPSAoX2xhc3Rfa2V5ID0gbGFzdCA9PT0gbnVsbCB8fCBsYXN0ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBsYXN0LmtleSkgIT09IG51bGwgJiYgX2xhc3Rfa2V5ICE9PSB2b2lkIDAgPyBfbGFzdF9rZXkgOiBudWxsO1xuICAgIH1cbn1cblxuXG5leHBvcnQgeyRhMDJkNTcwNDlkMjAyNjk1JGV4cG9ydCRkMDg1ZmI5ZTkyMGI1Y2E3IGFzIExpc3RDb2xsZWN0aW9ufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUxpc3RDb2xsZWN0aW9uLm1vZHVsZS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+list@3.11.2_react@18.3.1/node_modules/@react-stately/list/dist/ListCollection.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+list@3.11.2_react@18.3.1/node_modules/@react-stately/list/dist/useListState.mjs":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+list@3.11.2_react@18.3.1/node_modules/@react-stately/list/dist/useListState.mjs ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useListState: () => (/* binding */ $e72dd72e1c76a225$export$2f645645f7bca764)\n/* harmony export */ });\n/* harmony import */ var _ListCollection_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ListCollection.mjs */ \"(ssr)/./node_modules/.pnpm/@react-stately+list@3.11.2_react@18.3.1/node_modules/@react-stately/list/dist/ListCollection.mjs\");\n/* harmony import */ var _react_stately_selection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-stately/selection */ \"(ssr)/./node_modules/.pnpm/@react-stately+selection@3.19.0_react@18.3.1/node_modules/@react-stately/selection/dist/useMultipleSelectionState.mjs\");\n/* harmony import */ var _react_stately_selection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-stately/selection */ \"(ssr)/./node_modules/.pnpm/@react-stately+selection@3.19.0_react@18.3.1/node_modules/@react-stately/selection/dist/SelectionManager.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_stately_collections__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-stately/collections */ \"(ssr)/./node_modules/.pnpm/@react-stately+collections@3.12.1_react@18.3.1/node_modules/@react-stately/collections/dist/useCollection.mjs\");\n\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n\nfunction $e72dd72e1c76a225$export$2f645645f7bca764(props) {\n    let { filter: filter, layoutDelegate: layoutDelegate } = props;\n    let selectionState = (0, _react_stately_selection__WEBPACK_IMPORTED_MODULE_1__.useMultipleSelectionState)(props);\n    let disabledKeys = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>props.disabledKeys ? new Set(props.disabledKeys) : new Set(), [\n        props.disabledKeys\n    ]);\n    let factory = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((nodes)=>filter ? new (0, _ListCollection_mjs__WEBPACK_IMPORTED_MODULE_2__.ListCollection)(filter(nodes)) : new (0, _ListCollection_mjs__WEBPACK_IMPORTED_MODULE_2__.ListCollection)(nodes), [\n        filter\n    ]);\n    let context = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            suppressTextValueWarning: props.suppressTextValueWarning\n        }), [\n        props.suppressTextValueWarning\n    ]);\n    let collection = (0, _react_stately_collections__WEBPACK_IMPORTED_MODULE_3__.useCollection)(props, factory, context);\n    let selectionManager = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>new (0, _react_stately_selection__WEBPACK_IMPORTED_MODULE_4__.SelectionManager)(collection, selectionState, {\n            layoutDelegate: layoutDelegate\n        }), [\n        collection,\n        selectionState,\n        layoutDelegate\n    ]);\n    // Reset focused key if that item is deleted from the collection.\n    const cachedCollection = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (selectionState.focusedKey != null && !collection.getItem(selectionState.focusedKey) && cachedCollection.current) {\n            const startItem = cachedCollection.current.getItem(selectionState.focusedKey);\n            const cachedItemNodes = [\n                ...cachedCollection.current.getKeys()\n            ].map((key)=>{\n                const itemNode = cachedCollection.current.getItem(key);\n                return (itemNode === null || itemNode === void 0 ? void 0 : itemNode.type) === 'item' ? itemNode : null;\n            }).filter((node)=>node !== null);\n            const itemNodes = [\n                ...collection.getKeys()\n            ].map((key)=>{\n                const itemNode = collection.getItem(key);\n                return (itemNode === null || itemNode === void 0 ? void 0 : itemNode.type) === 'item' ? itemNode : null;\n            }).filter((node)=>node !== null);\n            var _cachedItemNodes_length, _itemNodes_length;\n            const diff = ((_cachedItemNodes_length = cachedItemNodes === null || cachedItemNodes === void 0 ? void 0 : cachedItemNodes.length) !== null && _cachedItemNodes_length !== void 0 ? _cachedItemNodes_length : 0) - ((_itemNodes_length = itemNodes === null || itemNodes === void 0 ? void 0 : itemNodes.length) !== null && _itemNodes_length !== void 0 ? _itemNodes_length : 0);\n            var _startItem_index, _startItem_index1, _itemNodes_length1;\n            let index = Math.min(diff > 1 ? Math.max(((_startItem_index = startItem === null || startItem === void 0 ? void 0 : startItem.index) !== null && _startItem_index !== void 0 ? _startItem_index : 0) - diff + 1, 0) : (_startItem_index1 = startItem === null || startItem === void 0 ? void 0 : startItem.index) !== null && _startItem_index1 !== void 0 ? _startItem_index1 : 0, ((_itemNodes_length1 = itemNodes === null || itemNodes === void 0 ? void 0 : itemNodes.length) !== null && _itemNodes_length1 !== void 0 ? _itemNodes_length1 : 0) - 1);\n            let newNode = null;\n            let isReverseSearching = false;\n            while(index >= 0){\n                if (!selectionManager.isDisabled(itemNodes[index].key)) {\n                    newNode = itemNodes[index];\n                    break;\n                }\n                // Find next, not disabled item.\n                if (index < itemNodes.length - 1 && !isReverseSearching) index++;\n                else {\n                    isReverseSearching = true;\n                    var _startItem_index2, _startItem_index3;\n                    if (index > ((_startItem_index2 = startItem === null || startItem === void 0 ? void 0 : startItem.index) !== null && _startItem_index2 !== void 0 ? _startItem_index2 : 0)) index = (_startItem_index3 = startItem === null || startItem === void 0 ? void 0 : startItem.index) !== null && _startItem_index3 !== void 0 ? _startItem_index3 : 0;\n                    index--;\n                }\n            }\n            selectionState.setFocusedKey(newNode ? newNode.key : null);\n        }\n        cachedCollection.current = collection;\n    }, [\n        collection,\n        selectionManager,\n        selectionState,\n        selectionState.focusedKey\n    ]);\n    return {\n        collection: collection,\n        disabledKeys: disabledKeys,\n        selectionManager: selectionManager\n    };\n}\n\n\n\n//# sourceMappingURL=useListState.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+list@3.11.2_react@18.3.1/node_modules/@react-stately/list/dist/useListState.mjs\n");

/***/ })

};
;