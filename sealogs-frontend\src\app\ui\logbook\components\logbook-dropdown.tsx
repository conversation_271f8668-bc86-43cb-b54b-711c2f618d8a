'use client'

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, XCircle } from 'lucide-react'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import { Separator } from '@/components/ui/separator'
import { useQueryState } from 'nuqs'
import { SealogsCogIcon } from '@/app/lib/icons'
import { cn } from '@/app/lib/utils'
import Link from 'next/link'
import { Button } from '@/components/ui'
import { useEffect, useState } from 'react'
import { displayField } from '@/app/ui/daily-checks/actions'
import { getFilteredFields } from '@/app/ui/daily-checks/actions'
import { SLALL_LogBookFields } from '@/app/lib/vesselDefaultConfig'
import TripLogFields from '@/app/lib/logbook-configuration/fields/trip-log'
import { sortCustomisedComponentFields } from '../../vessels/actions'

interface MenuItem {
    label: string
    value: string
}

interface LogbookActionMenuProps {
    items: MenuItem[]
    onBack: () => void
    onDelete: () => void
    showDelete?: boolean
    setOpen?: any
    logBookConfig?: any
}

export function LogbookActionMenu({
    items,
    onBack,
    onDelete,
    showDelete = false,
    setOpen,
    logBookConfig,
}: LogbookActionMenuProps) {
    // Use nuqs to manage the tab state through URL query parameters
    const [tab, setTab] = useQueryState('tab', { defaultValue: 'crew' })
    const [radioLogField, setRadioLogField] = useState<any>()

    const handleSelect = async (value: string) => {
        if (value !== tab) {
            await setTab(value)
        }
    }

    useEffect(() => {
        if (logBookConfig) {
            setRadioLogField(
                logBookConfig.customisedLogBookComponents?.nodes
                    .find((node: any) => node.title === 'Trip Log')
                    .customisedComponentFields.nodes.find(
                        (node: any) => node.fieldName === 'RadioLog',
                    ),
            )
        }
    }, [logBookConfig])

    return (
        <DropdownMenu>
            <DropdownMenuTrigger className="h-10 flex items-center">
                <SealogsCogIcon size={36} />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[256px] p-0">
                <div className="text-outer-space-500 flex flex-col items-center justify-center py-[9px]">
                    <DropdownMenuItem
                        onClick={onBack}
                        className={cn(
                            'flex items-center gap-[8px] px-[26px] w-full h-11 rounded-md cursor-pointer focus:bg-curious-blue-50',
                            'hover:bg-curious-blue-50 hover:px-[6px] hover:w-[233px] hover:border hover:border-border',
                        )}>
                        <ArrowLeft size={16} />
                        <span>Back to vessel</span>
                    </DropdownMenuItem>

                    {items.map((item, index) => (
                        <DropdownMenuItem
                            key={index}
                            onClick={() => handleSelect(item.value)}
                            className={cn(
                                'flex flex-row w-full group relative m-0 py-0 gap-[11px] justify-start px-[26px] h-11 cursor-pointer focus:bg-curious-blue-50 rounded-none',
                            )}>
                            {/* Circle indicator */}
                            <div className="h-full w-fit flex z-10 relative flex-col items-center justify-center">
                                <div
                                    className={cn(
                                        'border-l -translate-y-[17.5px] absolute inset-y-0 border-wedgewood-200 border-dashed',
                                        index === 0 && 'invisible',
                                    )}
                                />
                                <div
                                    className={cn(
                                        'size-[11px] z-10 rounded-full',
                                        'group-hover:border-primary group-hover:bg-curious-blue-200',
                                        tab === item.value
                                            ? 'border border-primary bg-curious-blue-200'
                                            : 'border border-cool-wedgewood-200 bg-outer-space-50',
                                    )}
                                />
                            </div>
                            <div
                                className={cn(
                                    'relative z-20',
                                    tab === item.value &&
                                        'font-medium text-accent',
                                )}>
                                {item.label}
                            </div>
                            <div
                                className={cn(
                                    'absolute w-full inset-0 mx-auto',
                                    'group-hover:bg-outspace-50 group-hover:px-[6px] rounded-md group-hover:w-[233px] group-hover:border group-hover:border-border',
                                    'will-change-transform will-change-width will-change-padding transform-gpu',
                                    'transition-[width,padding] ease-out duration-600',
                                    'outline-none focus:outline-none active:outline-none',
                                )}
                            />
                        </DropdownMenuItem>
                    ))}
                </div>
                {logBookConfig && radioLogField && (
                    <>
                        {radioLogField.status !== 'Off' && (
                            <>
                                <Separator className="border-border" />
                                <DropdownMenuItem
                                    className={cn(
                                        'px-[47px] h-[61px] text-text hover:text-foreground flex gap-2.5 group py-[21px] relative focus:bg-outer-space-50 rounded-none',
                                    )}>
                                    <span className="relative z-20">
                                        <Button
                                            variant="ghost"
                                            onClick={() => {
                                                setOpen(true)
                                            }}
                                            className="text-text uppercase hover:text-primary text-sm">
                                            Radio logs
                                        </Button>
                                    </span>
                                    <div
                                        className={cn(
                                            'absolute w-20 left-12 inset-y-0',
                                            'group-hover:bg-outer-space-50 group-hover:w-full group-hover:left-0',
                                            'will-change-transform will-change-width will-change-padding transform-gpu',
                                            'group-hover:transition-[width,left] group-hover:ease-out group-hover:duration-300',
                                            'outline-none focus:outline-none active:outline-none',
                                        )}
                                    />
                                </DropdownMenuItem>
                            </>
                        )}
                    </>
                )}
                {showDelete && (
                    <>
                        <Separator className="border-border" />
                        <DropdownMenuItem
                            onClick={onDelete}
                            className={cn(
                                'group relative h-[61px] px-[26px] py-[21px] cursor-pointer focus:bg-cinnabar-100 rounded-none text-destructive focus:text-destructive',
                            )}>
                            <div className="relative gap-2.5 flex items-center z-20">
                                <XCircle size={24} />
                                <span>Delete logbook entry</span>
                            </div>
                            <div
                                className={cn(
                                    'absolute w-full h-11 bottom-0 inset-x-0',
                                    'group-hover:bg-bg-cinnabar-100 group-hover:h-full',
                                    'will-change-transform will-change-width will-change-padding transform-gpu',
                                    'group-hover:transition-[height,color] group-hover:ease-out group-hover:duration-300',
                                    'outline-none focus:outline-none active:outline-none',
                                )}
                            />
                        </DropdownMenuItem>
                    </>
                )}
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
