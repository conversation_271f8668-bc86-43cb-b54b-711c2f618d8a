"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+live-announcer@3.4.1";
exports.ids = ["vendor-chunks/@react-aria+live-announcer@3.4.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+live-announcer@3.4.1/node_modules/@react-aria/live-announcer/dist/LiveAnnouncer.mjs":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+live-announcer@3.4.1/node_modules/@react-aria/live-announcer/dist/LiveAnnouncer.mjs ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   announce: () => (/* binding */ $319e236875307eab$export$a9b970dcc4ae71a9),\n/* harmony export */   clearAnnouncer: () => (/* binding */ $319e236875307eab$export$d10ae4f68404609a),\n/* harmony export */   destroyAnnouncer: () => (/* binding */ $319e236875307eab$export$d8686216b8b81b2f)\n/* harmony export */ });\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ /* Inspired by https://github.com/AlmeroSteyn/react-aria-live */ const $319e236875307eab$var$LIVEREGION_TIMEOUT_DELAY = 7000;\nlet $319e236875307eab$var$liveAnnouncer = null;\nfunction $319e236875307eab$export$a9b970dcc4ae71a9(message, assertiveness = 'assertive', timeout = $319e236875307eab$var$LIVEREGION_TIMEOUT_DELAY) {\n    if (!$319e236875307eab$var$liveAnnouncer) {\n        $319e236875307eab$var$liveAnnouncer = new $319e236875307eab$var$LiveAnnouncer();\n        // wait for the live announcer regions to be added to the dom, then announce\n        // otherwise Safari won't announce the message if it's added too quickly\n        // found most times less than 100ms were not consistent when announcing with Safari\n        // IS_REACT_ACT_ENVIRONMENT is used by React 18. Previous versions checked for the `jest` global.\n        // https://github.com/reactwg/react-18/discussions/102\n        // if we're in a test environment, announce without waiting\n        // @ts-ignore\n        if (!(typeof IS_REACT_ACT_ENVIRONMENT === 'boolean' ? IS_REACT_ACT_ENVIRONMENT : typeof jest !== 'undefined')) setTimeout(()=>{\n            if ($319e236875307eab$var$liveAnnouncer === null || $319e236875307eab$var$liveAnnouncer === void 0 ? void 0 : $319e236875307eab$var$liveAnnouncer.isAttached()) $319e236875307eab$var$liveAnnouncer === null || $319e236875307eab$var$liveAnnouncer === void 0 ? void 0 : $319e236875307eab$var$liveAnnouncer.announce(message, assertiveness, timeout);\n        }, 100);\n        else $319e236875307eab$var$liveAnnouncer.announce(message, assertiveness, timeout);\n    } else $319e236875307eab$var$liveAnnouncer.announce(message, assertiveness, timeout);\n}\nfunction $319e236875307eab$export$d10ae4f68404609a(assertiveness) {\n    if ($319e236875307eab$var$liveAnnouncer) $319e236875307eab$var$liveAnnouncer.clear(assertiveness);\n}\nfunction $319e236875307eab$export$d8686216b8b81b2f() {\n    if ($319e236875307eab$var$liveAnnouncer) {\n        $319e236875307eab$var$liveAnnouncer.destroy();\n        $319e236875307eab$var$liveAnnouncer = null;\n    }\n}\n// LiveAnnouncer is implemented using vanilla DOM, not React. That's because as of React 18\n// ReactDOM.render is deprecated, and the replacement, ReactDOM.createRoot is moved into a\n// subpath import `react-dom/client`. That makes it hard for us to support multiple React versions.\n// As a global API, we can't use portals without introducing a breaking API change. LiveAnnouncer\n// is simple enough to implement without React, so that's what we do here.\n// See this discussion for more details: https://github.com/reactwg/react-18/discussions/125#discussioncomment-2382638\nclass $319e236875307eab$var$LiveAnnouncer {\n    isAttached() {\n        var _this_node;\n        return (_this_node = this.node) === null || _this_node === void 0 ? void 0 : _this_node.isConnected;\n    }\n    createLog(ariaLive) {\n        let node = document.createElement('div');\n        node.setAttribute('role', 'log');\n        node.setAttribute('aria-live', ariaLive);\n        node.setAttribute('aria-relevant', 'additions');\n        return node;\n    }\n    destroy() {\n        if (!this.node) return;\n        document.body.removeChild(this.node);\n        this.node = null;\n    }\n    announce(message, assertiveness = 'assertive', timeout = $319e236875307eab$var$LIVEREGION_TIMEOUT_DELAY) {\n        var _this_assertiveLog, _this_politeLog;\n        if (!this.node) return;\n        let node = document.createElement('div');\n        if (typeof message === 'object') {\n            // To read an aria-labelledby, the element must have an appropriate role, such as img.\n            node.setAttribute('role', 'img');\n            node.setAttribute('aria-labelledby', message['aria-labelledby']);\n        } else node.textContent = message;\n        if (assertiveness === 'assertive') (_this_assertiveLog = this.assertiveLog) === null || _this_assertiveLog === void 0 ? void 0 : _this_assertiveLog.appendChild(node);\n        else (_this_politeLog = this.politeLog) === null || _this_politeLog === void 0 ? void 0 : _this_politeLog.appendChild(node);\n        if (message !== '') setTimeout(()=>{\n            node.remove();\n        }, timeout);\n    }\n    clear(assertiveness) {\n        if (!this.node) return;\n        if ((!assertiveness || assertiveness === 'assertive') && this.assertiveLog) this.assertiveLog.innerHTML = '';\n        if ((!assertiveness || assertiveness === 'polite') && this.politeLog) this.politeLog.innerHTML = '';\n    }\n    constructor(){\n        this.node = null;\n        this.assertiveLog = null;\n        this.politeLog = null;\n        if (typeof document !== 'undefined') {\n            this.node = document.createElement('div');\n            this.node.dataset.liveAnnouncer = 'true';\n            // copied from VisuallyHidden\n            Object.assign(this.node.style, {\n                border: 0,\n                clip: 'rect(0 0 0 0)',\n                clipPath: 'inset(50%)',\n                height: '1px',\n                margin: '-1px',\n                overflow: 'hidden',\n                padding: 0,\n                position: 'absolute',\n                width: '1px',\n                whiteSpace: 'nowrap'\n            });\n            this.assertiveLog = this.createLog('assertive');\n            this.node.appendChild(this.assertiveLog);\n            this.politeLog = this.createLog('polite');\n            this.node.appendChild(this.politeLog);\n            document.body.prepend(this.node);\n        }\n    }\n}\n\n\n\n//# sourceMappingURL=LiveAnnouncer.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+live-announcer@3.4.1/node_modules/@react-aria/live-announcer/dist/LiveAnnouncer.mjs\n");

/***/ })

};
;