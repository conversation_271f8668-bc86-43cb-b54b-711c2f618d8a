'use client'

import React, { useRef, useState } from 'react'
import CrewTrainingMatrix from './matrix'
import CrewTrainingList from './list'
import { NavData } from '@/components/app-sidebar'
import { H1, H2 } from '@/components/ui'
import { CrewTrainingFilterActions } from '@/components/filter/components/training-actions'
import { SealogsTrainingIcon } from '@/app/lib/icons/SealogsTrainingIcon'
import { ListHeader } from '@/components/ui/list-header'
import { SealogsCrewIcon } from '@/app/lib/icons/SealogsCrewIcon'

type FilterHandle = {
    apply: (p: { type: string; data: any }) => void
    overdue: boolean // read-only snapshot
    setOverdue: (v: boolean) => void
}

const CrewTrainingClient = () => {
    const applyFilterRef = useRef<FilterHandle>(null)

    /** ⬅️ 1) reactive state that drives the heading */
    const [isOverdueEnabled, setIsOverdueEnabled] = useState(false)

    /** ⬅️ 2) keep both the list and the heading in sync */
    const handleDropdownChange = (type: string, data: any) => {
        if (type === 'overdue') setIsOverdueEnabled(!!data)
        applyFilterRef.current?.apply({ type, data })
    }

    return (
        <>
            <ListHeader
                icon={
                    <SealogsTrainingIcon
                        className={`h-12 w-12 ring-1 p-0.5 rounded-full bg-background`}
                    />
                }
                title="Crew training"
            />
            <CrewTrainingMatrix />
            <div className="bg-background phablet:bg-muted pb-[7px] z-50 sticky gap-4 mt-32 pt-6 inset-0 flex items-start justify-between flex-nowrap">
                <div className="flex py-3 items-baseline">
                    <H2>
                        {!isOverdueEnabled
                            ? 'Overdue and upcoming'
                            : 'Completed'}{' '}
                        trainings
                    </H2>
                </div>

                <CrewTrainingFilterActions
                    onChange={(data: any) => {
                        handleDropdownChange('overdue', data)
                    }}
                    overdueList={isOverdueEnabled}
                />
            </div>
            <CrewTrainingList applyFilterRef={applyFilterRef} />
        </>
    )
}

export default CrewTrainingClient
