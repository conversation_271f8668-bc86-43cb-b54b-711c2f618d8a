"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+dialog@3.5.21_r_9b3b3a589bde9981d8f8667d98b7de24";
exports.ids = ["vendor-chunks/@react-aria+dialog@3.5.21_r_9b3b3a589bde9981d8f8667d98b7de24"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+dialog@3.5.21_r_9b3b3a589bde9981d8f8667d98b7de24/node_modules/@react-aria/dialog/dist/useDialog.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+dialog@3.5.21_r_9b3b3a589bde9981d8f8667d98b7de24/node_modules/@react-aria/dialog/dist/useDialog.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDialog: () => (/* binding */ $40df3f8667284809$export$d55e7ee900f34e93)\n/* harmony export */ });\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/useId.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/filterDOMProps.mjs\");\n/* harmony import */ var _react_aria_focus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/focus */ \"(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/focusSafely.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_aria_overlays__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/overlays */ \"(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.25.0_1db6844116d1aee1b0fd66632f05b455/node_modules/@react-aria/overlays/dist/Overlay.mjs\");\n\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n\nfunction $40df3f8667284809$export$d55e7ee900f34e93(props, ref) {\n    let { role: role = 'dialog' } = props;\n    let titleId = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useSlotId)();\n    titleId = props['aria-label'] ? undefined : titleId;\n    let isRefocusing = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Focus the dialog itself on mount, unless a child element is already focused.\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (ref.current && !ref.current.contains(document.activeElement)) {\n            (0, _react_aria_focus__WEBPACK_IMPORTED_MODULE_2__.focusSafely)(ref.current);\n            // Safari on iOS does not move the VoiceOver cursor to the dialog\n            // or announce that it has opened until it has rendered. A workaround\n            // is to wait for half a second, then blur and re-focus the dialog.\n            let timeout = setTimeout(()=>{\n                if (document.activeElement === ref.current) {\n                    isRefocusing.current = true;\n                    if (ref.current) {\n                        ref.current.blur();\n                        (0, _react_aria_focus__WEBPACK_IMPORTED_MODULE_2__.focusSafely)(ref.current);\n                    }\n                    isRefocusing.current = false;\n                }\n            }, 500);\n            return ()=>{\n                clearTimeout(timeout);\n            };\n        }\n    }, [\n        ref\n    ]);\n    (0, _react_aria_overlays__WEBPACK_IMPORTED_MODULE_3__.useOverlayFocusContain)();\n    // We do not use aria-modal due to a Safari bug which forces the first focusable element to be focused\n    // on mount when inside an iframe, no matter which element we programmatically focus.\n    // See https://bugs.webkit.org/show_bug.cgi?id=211934.\n    // useModal sets aria-hidden on all elements outside the dialog, so the dialog will behave as a modal\n    // even without aria-modal on the dialog itself.\n    return {\n        dialogProps: {\n            ...(0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__.filterDOMProps)(props, {\n                labelable: true\n            }),\n            role: role,\n            tabIndex: -1,\n            'aria-labelledby': props['aria-labelledby'] || titleId,\n            // Prevent blur events from reaching useOverlay, which may cause\n            // popovers to close. Since focus is contained within the dialog,\n            // we don't want this to occur due to the above useEffect.\n            onBlur: (e)=>{\n                if (isRefocusing.current) e.stopPropagation();\n            }\n        },\n        titleProps: {\n            id: titleId\n        }\n    };\n}\n\n\n\n//# sourceMappingURL=useDialog.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+dialog@3.5.21_r_9b3b3a589bde9981d8f8667d98b7de24/node_modules/@react-aria/dialog/dist/useDialog.mjs\n");

/***/ })

};
;