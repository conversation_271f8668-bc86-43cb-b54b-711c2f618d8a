'use client'

import * as React from 'react'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { useSidebar } from '@/components/ui/sidebar'
import Link from 'next/link'
import { SealogsCogIcon } from '@/app/lib/icons'

export const DocumentLockerFilterActions = () => {
    const { isMobile } = useSidebar()

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <SealogsCogIcon size={36} />
            </DropdownMenuTrigger>
            <DropdownMenuContent
                side={isMobile ? 'bottom' : 'right'}
                align={isMobile ? 'end' : 'start'}>
                <DropdownMenuItem disabled>
                    Upload Document
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
