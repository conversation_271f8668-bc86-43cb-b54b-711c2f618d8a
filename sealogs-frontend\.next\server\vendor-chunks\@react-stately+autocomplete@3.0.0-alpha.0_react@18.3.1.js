"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-stately+autocomplete@3.0.0-alpha.0_react@18.3.1";
exports.ids = ["vendor-chunks/@react-stately+autocomplete@3.0.0-alpha.0_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+autocomplete@3.0.0-alpha.0_react@18.3.1/node_modules/@react-stately/autocomplete/dist/useAutocompleteState.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+autocomplete@3.0.0-alpha.0_react@18.3.1/node_modules/@react-stately/autocomplete/dist/useAutocompleteState.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UNSTABLE_useAutocompleteState: () => (/* binding */ $94680be9a48a8f2d$export$130b939e942a109d)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_stately_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-stately/utils */ \"(ssr)/./node_modules/.pnpm/@react-stately+utils@3.10.5_react@18.3.1/node_modules/@react-stately/utils/dist/useControlledState.mjs\");\n\n\n\n/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\nfunction $94680be9a48a8f2d$export$130b939e942a109d(props) {\n    let { onInputChange: propsOnInputChange, inputValue: propsInputValue, defaultInputValue: propsDefaultInputValue = '' } = props;\n    let onInputChange = (value)=>{\n        if (propsOnInputChange) propsOnInputChange(value);\n    };\n    let [focusedNodeId, setFocusedNodeId] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    let [inputValue, setInputValue] = (0, _react_stately_utils__WEBPACK_IMPORTED_MODULE_1__.useControlledState)(propsInputValue, propsDefaultInputValue, onInputChange);\n    return {\n        inputValue: inputValue,\n        setInputValue: setInputValue,\n        focusedNodeId: focusedNodeId,\n        setFocusedNodeId: setFocusedNodeId\n    };\n}\n\n\n\n//# sourceMappingURL=useAutocompleteState.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+autocomplete@3.0.0-alpha.0_react@18.3.1/node_modules/@react-stately/autocomplete/dist/useAutocompleteState.mjs\n");

/***/ })

};
;