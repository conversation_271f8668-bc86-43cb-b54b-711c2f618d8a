'use client'

import { VESSEL_BRIEF_LIST } from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'

import { useEffect, useState } from 'react'
import { Combobox } from '@/components/ui/comboBox'

const VesselDropdown = ({
    value,
    onChange,
    isClearable = false,
    className = '',
    vesselIdOptions = [],
    filterByTrainingSessionMemberId = 0,
}: any) => {
    const [isLoading, setIsLoading] = useState(true)
    const [vesselList, setVesselList] = useState([] as any)
    const [allVesselList, setAllVesselList] = useState([] as any)
    const [selectedVessel, setSelectedVessel] = useState([] as any)
    const [queryVesselList, { loading: queryVesselListLoading }] = useLazyQuery(
        VESSEL_BRIEF_LIST,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readVessels.nodes

                if (data) {
                    const filteredData = data.filter(
                        (vessel: any) => !vessel.archived && vessel.title,
                    )
                    const formattedData = filteredData.map((vessel: any) => ({
                        value: vessel.id,
                        label: vessel.title,
                    }))
                    formattedData.sort((a: any, b: any) =>
                        a.label.localeCompare(b.label),
                    )
                    setAllVesselList(formattedData)
                    setVesselList(formattedData)
                    vesselIdOptions = vesselList
                    setSelectedVessel(
                        formattedData.find(
                            (vessel: any) => vessel.value === value,
                        ),
                    )
                }
            },
            onError: (error: any) => {
                console.error('queryVesselList error', error)
            },
        },
    )
    const loadVesselList = async () => {
        let filter = {}
        if (filterByTrainingSessionMemberId > 0) {
            filter = {
                trainingSessions: {
                    members: {
                        id: { contains: filterByTrainingSessionMemberId },
                    },
                },
            }
        }
        filter = {
            ...filter,
            archived: { eq: false },
        }
        queryVesselList({
            variables: { filter: filter },
        })
    }
    useEffect(() => {
        if (isLoading) {
            loadVesselList()
            setIsLoading(false)
        }
    }, [isLoading])
    useEffect(() => {
        setSelectedVessel(
            vesselList.find((vessel: any) => vessel.value === value),
        )
    }, [value])
    useEffect(() => {
        if (vesselIdOptions.length > 0) {
            const filteredVesselList = allVesselList.filter((v: any) =>
                vesselIdOptions.includes(v.value),
            )
            setVesselList(filteredVesselList)
        } else {
            // If no options are provided, show the full list
            setVesselList(allVesselList)
        }
    }, [vesselIdOptions, allVesselList])
    return (
        <Combobox
            options={vesselList}
            value={selectedVessel}
            onChange={(selectedOption: any) => {
                setSelectedVessel(selectedOption)
                onChange(selectedOption)
            }}
            isLoading={queryVesselListLoading && vesselList && !isLoading}
            title="Vessel"
            className={className}
            placeholder="Vessel"
            multi={true}
        />
    )
}

export default VesselDropdown
