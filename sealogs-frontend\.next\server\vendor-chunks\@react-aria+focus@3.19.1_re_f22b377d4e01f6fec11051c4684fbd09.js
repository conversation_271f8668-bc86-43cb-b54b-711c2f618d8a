"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09";
exports.ids = ["vendor-chunks/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/FocusScope.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/FocusScope.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ $9bf71ea28793e738$export$20e40289641fbbb6),\n/* harmony export */   createFocusManager: () => (/* binding */ $9bf71ea28793e738$export$c5251b9e124bf29),\n/* harmony export */   focusScopeTree: () => (/* binding */ $9bf71ea28793e738$export$d06fae2ee68b101e),\n/* harmony export */   getFocusableTreeWalker: () => (/* binding */ $9bf71ea28793e738$export$2d6ec8fc375ceafa),\n/* harmony export */   isElementInChildOfActiveScope: () => (/* binding */ $9bf71ea28793e738$export$1258395f99bf9cbf),\n/* harmony export */   isFocusable: () => (/* binding */ $9bf71ea28793e738$export$4c063cf1350e6fed),\n/* harmony export */   useFocusManager: () => (/* binding */ $9bf71ea28793e738$export$10c5169755ce7bd7)\n/* harmony export */ });\n/* harmony import */ var _focusSafely_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./focusSafely.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/focusSafely.mjs\");\n/* harmony import */ var _isElementVisible_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./isElementVisible.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/isElementVisible.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._697c5b52c6876ffd3e2260945ec4674e/node_modules/@react-aria/interactions/dist/useFocusVisible.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/useLayoutEffect.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/domHelpers.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/platform.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n\n\nconst $9bf71ea28793e738$var$FocusContext = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createContext(null);\nconst $9bf71ea28793e738$var$RESTORE_FOCUS_EVENT = 'react-aria-focus-scope-restore';\nlet $9bf71ea28793e738$var$activeScope = null;\nfunction $9bf71ea28793e738$export$20e40289641fbbb6(props) {\n    let { children: children, contain: contain, restoreFocus: restoreFocus, autoFocus: autoFocus } = props;\n    let startRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    let endRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    let scopeRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    let { parentNode: parentNode } = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($9bf71ea28793e738$var$FocusContext) || {};\n    // Create a tree node here so we can add children to it even before it is added to the tree.\n    let node = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>new $9bf71ea28793e738$var$TreeNode({\n            scopeRef: scopeRef\n        }), [\n        scopeRef\n    ]);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        // If a new scope mounts outside the active scope, (e.g. DialogContainer launched from a menu),\n        // use the active scope as the parent instead of the parent from context. Layout effects run bottom\n        // up, so if the parent is not yet added to the tree, don't do this. Only the outer-most FocusScope\n        // that is being added should get the activeScope as its parent.\n        let parent = parentNode || $9bf71ea28793e738$export$d06fae2ee68b101e.root;\n        if ($9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(parent.scopeRef) && $9bf71ea28793e738$var$activeScope && !$9bf71ea28793e738$var$isAncestorScope($9bf71ea28793e738$var$activeScope, parent.scopeRef)) {\n            let activeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode($9bf71ea28793e738$var$activeScope);\n            if (activeNode) parent = activeNode;\n        }\n        // Add the node to the parent, and to the tree.\n        parent.addChild(node);\n        $9bf71ea28793e738$export$d06fae2ee68b101e.addNode(node);\n    }, [\n        node,\n        parentNode\n    ]);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        let node = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef);\n        if (node) node.contain = !!contain;\n    }, [\n        contain\n    ]);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        var _startRef_current;\n        // Find all rendered nodes between the sentinels and add them to the scope.\n        let node = (_startRef_current = startRef.current) === null || _startRef_current === void 0 ? void 0 : _startRef_current.nextSibling;\n        let nodes = [];\n        let stopPropagation = (e)=>e.stopPropagation();\n        while(node && node !== endRef.current){\n            nodes.push(node);\n            // Stop custom restore focus event from propagating to parent focus scopes.\n            node.addEventListener($9bf71ea28793e738$var$RESTORE_FOCUS_EVENT, stopPropagation);\n            node = node.nextSibling;\n        }\n        scopeRef.current = nodes;\n        return ()=>{\n            for (let node of nodes)node.removeEventListener($9bf71ea28793e738$var$RESTORE_FOCUS_EVENT, stopPropagation);\n        };\n    }, [\n        children\n    ]);\n    $9bf71ea28793e738$var$useActiveScopeTracker(scopeRef, restoreFocus, contain);\n    $9bf71ea28793e738$var$useFocusContainment(scopeRef, contain);\n    $9bf71ea28793e738$var$useRestoreFocus(scopeRef, restoreFocus, contain);\n    $9bf71ea28793e738$var$useAutoFocus(scopeRef, autoFocus);\n    // This needs to be an effect so that activeScope is updated after the FocusScope tree is complete.\n    // It cannot be a useLayoutEffect because the parent of this node hasn't been attached in the tree yet.\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const activeElement = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined).activeElement;\n        let scope = null;\n        if ($9bf71ea28793e738$var$isElementInScope(activeElement, scopeRef.current)) {\n            // We need to traverse the focusScope tree and find the bottom most scope that\n            // contains the active element and set that as the activeScope.\n            for (let node of $9bf71ea28793e738$export$d06fae2ee68b101e.traverse())if (node.scopeRef && $9bf71ea28793e738$var$isElementInScope(activeElement, node.scopeRef.current)) scope = node;\n            if (scope === $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef)) $9bf71ea28793e738$var$activeScope = scope.scopeRef;\n        }\n    }, [\n        scopeRef\n    ]);\n    // This layout effect cleanup is so that the tree node is removed synchronously with react before the RAF\n    // in useRestoreFocus cleanup runs.\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        return ()=>{\n            var _focusScopeTree_getTreeNode_parent, _focusScopeTree_getTreeNode;\n            var _focusScopeTree_getTreeNode_parent_scopeRef;\n            // Scope may have been re-parented.\n            let parentScope = (_focusScopeTree_getTreeNode_parent_scopeRef = (_focusScopeTree_getTreeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef)) === null || _focusScopeTree_getTreeNode === void 0 ? void 0 : (_focusScopeTree_getTreeNode_parent = _focusScopeTree_getTreeNode.parent) === null || _focusScopeTree_getTreeNode_parent === void 0 ? void 0 : _focusScopeTree_getTreeNode_parent.scopeRef) !== null && _focusScopeTree_getTreeNode_parent_scopeRef !== void 0 ? _focusScopeTree_getTreeNode_parent_scopeRef : null;\n            if ((scopeRef === $9bf71ea28793e738$var$activeScope || $9bf71ea28793e738$var$isAncestorScope(scopeRef, $9bf71ea28793e738$var$activeScope)) && (!parentScope || $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(parentScope))) $9bf71ea28793e738$var$activeScope = parentScope;\n            $9bf71ea28793e738$export$d06fae2ee68b101e.removeTreeNode(scopeRef);\n        };\n    }, [\n        scopeRef\n    ]);\n    let focusManager = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>$9bf71ea28793e738$var$createFocusManagerForScope(scopeRef), []);\n    let value = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            focusManager: focusManager,\n            parentNode: node\n        }), [\n        node,\n        focusManager\n    ]);\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($9bf71ea28793e738$var$FocusContext.Provider, {\n        value: value\n    }, /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement(\"span\", {\n        \"data-focus-scope-start\": true,\n        hidden: true,\n        ref: startRef\n    }), children, /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement(\"span\", {\n        \"data-focus-scope-end\": true,\n        hidden: true,\n        ref: endRef\n    }));\n}\nfunction $9bf71ea28793e738$export$10c5169755ce7bd7() {\n    var _useContext;\n    return (_useContext = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($9bf71ea28793e738$var$FocusContext)) === null || _useContext === void 0 ? void 0 : _useContext.focusManager;\n}\nfunction $9bf71ea28793e738$var$createFocusManagerForScope(scopeRef) {\n    return {\n        focusNext (opts = {}) {\n            let scope = scopeRef.current;\n            let { from: from, tabbable: tabbable, wrap: wrap, accept: accept } = opts;\n            let node = from || (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(scope[0]).activeElement;\n            let sentinel = scope[0].previousElementSibling;\n            let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n                tabbable: tabbable,\n                accept: accept\n            }, scope);\n            walker.currentNode = $9bf71ea28793e738$var$isElementInScope(node, scope) ? node : sentinel;\n            let nextNode = walker.nextNode();\n            if (!nextNode && wrap) {\n                walker.currentNode = sentinel;\n                nextNode = walker.nextNode();\n            }\n            if (nextNode) $9bf71ea28793e738$var$focusElement(nextNode, true);\n            return nextNode;\n        },\n        focusPrevious (opts = {}) {\n            let scope = scopeRef.current;\n            let { from: from, tabbable: tabbable, wrap: wrap, accept: accept } = opts;\n            let node = from || (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(scope[0]).activeElement;\n            let sentinel = scope[scope.length - 1].nextElementSibling;\n            let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n                tabbable: tabbable,\n                accept: accept\n            }, scope);\n            walker.currentNode = $9bf71ea28793e738$var$isElementInScope(node, scope) ? node : sentinel;\n            let previousNode = walker.previousNode();\n            if (!previousNode && wrap) {\n                walker.currentNode = sentinel;\n                previousNode = walker.previousNode();\n            }\n            if (previousNode) $9bf71ea28793e738$var$focusElement(previousNode, true);\n            return previousNode;\n        },\n        focusFirst (opts = {}) {\n            let scope = scopeRef.current;\n            let { tabbable: tabbable, accept: accept } = opts;\n            let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n                tabbable: tabbable,\n                accept: accept\n            }, scope);\n            walker.currentNode = scope[0].previousElementSibling;\n            let nextNode = walker.nextNode();\n            if (nextNode) $9bf71ea28793e738$var$focusElement(nextNode, true);\n            return nextNode;\n        },\n        focusLast (opts = {}) {\n            let scope = scopeRef.current;\n            let { tabbable: tabbable, accept: accept } = opts;\n            let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n                tabbable: tabbable,\n                accept: accept\n            }, scope);\n            walker.currentNode = scope[scope.length - 1].nextElementSibling;\n            let previousNode = walker.previousNode();\n            if (previousNode) $9bf71ea28793e738$var$focusElement(previousNode, true);\n            return previousNode;\n        }\n    };\n}\nconst $9bf71ea28793e738$var$focusableElements = [\n    'input:not([disabled]):not([type=hidden])',\n    'select:not([disabled])',\n    'textarea:not([disabled])',\n    'button:not([disabled])',\n    'a[href]',\n    'area[href]',\n    'summary',\n    'iframe',\n    'object',\n    'embed',\n    'audio[controls]',\n    'video[controls]',\n    '[contenteditable]:not([contenteditable^=\"false\"])'\n];\nconst $9bf71ea28793e738$var$FOCUSABLE_ELEMENT_SELECTOR = $9bf71ea28793e738$var$focusableElements.join(':not([hidden]),') + ',[tabindex]:not([disabled]):not([hidden])';\n$9bf71ea28793e738$var$focusableElements.push('[tabindex]:not([tabindex=\"-1\"]):not([disabled])');\nconst $9bf71ea28793e738$var$TABBABLE_ELEMENT_SELECTOR = $9bf71ea28793e738$var$focusableElements.join(':not([hidden]):not([tabindex=\"-1\"]),');\nfunction $9bf71ea28793e738$export$4c063cf1350e6fed(element) {\n    return element.matches($9bf71ea28793e738$var$FOCUSABLE_ELEMENT_SELECTOR);\n}\nfunction $9bf71ea28793e738$var$getScopeRoot(scope) {\n    return scope[0].parentElement;\n}\nfunction $9bf71ea28793e738$var$shouldContainFocus(scopeRef) {\n    let scope = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode($9bf71ea28793e738$var$activeScope);\n    while(scope && scope.scopeRef !== scopeRef){\n        if (scope.contain) return false;\n        scope = scope.parent;\n    }\n    return true;\n}\nfunction $9bf71ea28793e738$var$useFocusContainment(scopeRef, contain) {\n    let focusedNode = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(undefined);\n    let raf = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(undefined);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        let scope = scopeRef.current;\n        if (!contain) {\n            // if contain was changed, then we should cancel any ongoing waits to pull focus back into containment\n            if (raf.current) {\n                cancelAnimationFrame(raf.current);\n                raf.current = undefined;\n            }\n            return;\n        }\n        const ownerDocument = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(scope ? scope[0] : undefined);\n        // Handle the Tab key to contain focus within the scope\n        let onKeyDown = (e)=>{\n            if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !$9bf71ea28793e738$var$shouldContainFocus(scopeRef) || e.isComposing) return;\n            let focusedElement = ownerDocument.activeElement;\n            let scope = scopeRef.current;\n            if (!scope || !$9bf71ea28793e738$var$isElementInScope(focusedElement, scope)) return;\n            let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n                tabbable: true\n            }, scope);\n            if (!focusedElement) return;\n            walker.currentNode = focusedElement;\n            let nextElement = e.shiftKey ? walker.previousNode() : walker.nextNode();\n            if (!nextElement) {\n                walker.currentNode = e.shiftKey ? scope[scope.length - 1].nextElementSibling : scope[0].previousElementSibling;\n                nextElement = e.shiftKey ? walker.previousNode() : walker.nextNode();\n            }\n            e.preventDefault();\n            if (nextElement) $9bf71ea28793e738$var$focusElement(nextElement, true);\n        };\n        let onFocus = (e)=>{\n            // If focusing an element in a child scope of the currently active scope, the child becomes active.\n            // Moving out of the active scope to an ancestor is not allowed.\n            if ((!$9bf71ea28793e738$var$activeScope || $9bf71ea28793e738$var$isAncestorScope($9bf71ea28793e738$var$activeScope, scopeRef)) && $9bf71ea28793e738$var$isElementInScope(e.target, scopeRef.current)) {\n                $9bf71ea28793e738$var$activeScope = scopeRef;\n                focusedNode.current = e.target;\n            } else if ($9bf71ea28793e738$var$shouldContainFocus(scopeRef) && !$9bf71ea28793e738$var$isElementInChildScope(e.target, scopeRef)) {\n                // If a focus event occurs outside the active scope (e.g. user tabs from browser location bar),\n                // restore focus to the previously focused node or the first tabbable element in the active scope.\n                if (focusedNode.current) focusedNode.current.focus();\n                else if ($9bf71ea28793e738$var$activeScope && $9bf71ea28793e738$var$activeScope.current) $9bf71ea28793e738$var$focusFirstInScope($9bf71ea28793e738$var$activeScope.current);\n            } else if ($9bf71ea28793e738$var$shouldContainFocus(scopeRef)) focusedNode.current = e.target;\n        };\n        let onBlur = (e)=>{\n            // Firefox doesn't shift focus back to the Dialog properly without this\n            if (raf.current) cancelAnimationFrame(raf.current);\n            raf.current = requestAnimationFrame(()=>{\n                // Patches infinite focus coersion loop for Android Talkback where the user isn't able to move the virtual cursor\n                // if within a containing focus scope. Bug filed against Chrome: https://issuetracker.google.com/issues/384844019.\n                // Note that this means focus can leave focus containing modals due to this, but it is isolated to Chrome Talkback.\n                let modality = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_3__.getInteractionModality)();\n                let shouldSkipFocusRestore = (modality === 'virtual' || modality === null) && (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__.isAndroid)() && (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__.isChrome)();\n                // Use document.activeElement instead of e.relatedTarget so we can tell if user clicked into iframe\n                if (!shouldSkipFocusRestore && ownerDocument.activeElement && $9bf71ea28793e738$var$shouldContainFocus(scopeRef) && !$9bf71ea28793e738$var$isElementInChildScope(ownerDocument.activeElement, scopeRef)) {\n                    $9bf71ea28793e738$var$activeScope = scopeRef;\n                    if (ownerDocument.body.contains(e.target)) {\n                        var _focusedNode_current;\n                        focusedNode.current = e.target;\n                        (_focusedNode_current = focusedNode.current) === null || _focusedNode_current === void 0 ? void 0 : _focusedNode_current.focus();\n                    } else if ($9bf71ea28793e738$var$activeScope.current) $9bf71ea28793e738$var$focusFirstInScope($9bf71ea28793e738$var$activeScope.current);\n                }\n            });\n        };\n        ownerDocument.addEventListener('keydown', onKeyDown, false);\n        ownerDocument.addEventListener('focusin', onFocus, false);\n        scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.addEventListener('focusin', onFocus, false));\n        scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.addEventListener('focusout', onBlur, false));\n        return ()=>{\n            ownerDocument.removeEventListener('keydown', onKeyDown, false);\n            ownerDocument.removeEventListener('focusin', onFocus, false);\n            scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.removeEventListener('focusin', onFocus, false));\n            scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.removeEventListener('focusout', onBlur, false));\n        };\n    }, [\n        scopeRef,\n        contain\n    ]);\n    // This is a useLayoutEffect so it is guaranteed to run before our async synthetic blur\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        return ()=>{\n            if (raf.current) cancelAnimationFrame(raf.current);\n        };\n    }, [\n        raf\n    ]);\n}\nfunction $9bf71ea28793e738$var$isElementInAnyScope(element) {\n    return $9bf71ea28793e738$var$isElementInChildScope(element);\n}\nfunction $9bf71ea28793e738$var$isElementInScope(element, scope) {\n    if (!element) return false;\n    if (!scope) return false;\n    return scope.some((node)=>node.contains(element));\n}\nfunction $9bf71ea28793e738$var$isElementInChildScope(element, scope = null) {\n    // If the element is within a top layer element (e.g. toasts), always allow moving focus there.\n    if (element instanceof Element && element.closest('[data-react-aria-top-layer]')) return true;\n    // node.contains in isElementInScope covers child scopes that are also DOM children,\n    // but does not cover child scopes in portals.\n    for (let { scopeRef: s } of $9bf71ea28793e738$export$d06fae2ee68b101e.traverse($9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scope))){\n        if (s && $9bf71ea28793e738$var$isElementInScope(element, s.current)) return true;\n    }\n    return false;\n}\nfunction $9bf71ea28793e738$export$1258395f99bf9cbf(element) {\n    return $9bf71ea28793e738$var$isElementInChildScope(element, $9bf71ea28793e738$var$activeScope);\n}\nfunction $9bf71ea28793e738$var$isAncestorScope(ancestor, scope) {\n    var _focusScopeTree_getTreeNode;\n    let parent = (_focusScopeTree_getTreeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scope)) === null || _focusScopeTree_getTreeNode === void 0 ? void 0 : _focusScopeTree_getTreeNode.parent;\n    while(parent){\n        if (parent.scopeRef === ancestor) return true;\n        parent = parent.parent;\n    }\n    return false;\n}\nfunction $9bf71ea28793e738$var$focusElement(element, scroll = false) {\n    if (element != null && !scroll) try {\n        (0, _focusSafely_mjs__WEBPACK_IMPORTED_MODULE_5__.focusSafely)(element);\n    } catch  {\n    // ignore\n    }\n    else if (element != null) try {\n        element.focus();\n    } catch  {\n    // ignore\n    }\n}\nfunction $9bf71ea28793e738$var$getFirstInScope(scope, tabbable = true) {\n    let sentinel = scope[0].previousElementSibling;\n    let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n    let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n        tabbable: tabbable\n    }, scope);\n    walker.currentNode = sentinel;\n    let nextNode = walker.nextNode();\n    // If the scope does not contain a tabbable element, use the first focusable element.\n    if (tabbable && !nextNode) {\n        scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n        walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n            tabbable: false\n        }, scope);\n        walker.currentNode = sentinel;\n        nextNode = walker.nextNode();\n    }\n    return nextNode;\n}\nfunction $9bf71ea28793e738$var$focusFirstInScope(scope, tabbable = true) {\n    $9bf71ea28793e738$var$focusElement($9bf71ea28793e738$var$getFirstInScope(scope, tabbable));\n}\nfunction $9bf71ea28793e738$var$useAutoFocus(scopeRef, autoFocus) {\n    const autoFocusRef = (0, react__WEBPACK_IMPORTED_MODULE_0__).useRef(autoFocus);\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (autoFocusRef.current) {\n            $9bf71ea28793e738$var$activeScope = scopeRef;\n            const ownerDocument = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined);\n            if (!$9bf71ea28793e738$var$isElementInScope(ownerDocument.activeElement, $9bf71ea28793e738$var$activeScope.current) && scopeRef.current) $9bf71ea28793e738$var$focusFirstInScope(scopeRef.current);\n        }\n        autoFocusRef.current = false;\n    }, [\n        scopeRef\n    ]);\n}\nfunction $9bf71ea28793e738$var$useActiveScopeTracker(scopeRef, restore, contain) {\n    // tracks the active scope, in case restore and contain are both false.\n    // if either are true, this is tracked in useRestoreFocus or useFocusContainment.\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        if (restore || contain) return;\n        let scope = scopeRef.current;\n        const ownerDocument = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(scope ? scope[0] : undefined);\n        let onFocus = (e)=>{\n            let target = e.target;\n            if ($9bf71ea28793e738$var$isElementInScope(target, scopeRef.current)) $9bf71ea28793e738$var$activeScope = scopeRef;\n            else if (!$9bf71ea28793e738$var$isElementInAnyScope(target)) $9bf71ea28793e738$var$activeScope = null;\n        };\n        ownerDocument.addEventListener('focusin', onFocus, false);\n        scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.addEventListener('focusin', onFocus, false));\n        return ()=>{\n            ownerDocument.removeEventListener('focusin', onFocus, false);\n            scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.removeEventListener('focusin', onFocus, false));\n        };\n    }, [\n        scopeRef,\n        restore,\n        contain\n    ]);\n}\nfunction $9bf71ea28793e738$var$shouldRestoreFocus(scopeRef) {\n    let scope = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode($9bf71ea28793e738$var$activeScope);\n    while(scope && scope.scopeRef !== scopeRef){\n        if (scope.nodeToRestore) return false;\n        scope = scope.parent;\n    }\n    return (scope === null || scope === void 0 ? void 0 : scope.scopeRef) === scopeRef;\n}\nfunction $9bf71ea28793e738$var$useRestoreFocus(scopeRef, restoreFocus, contain) {\n    // create a ref during render instead of useLayoutEffect so the active element is saved before a child with autoFocus=true mounts.\n    // eslint-disable-next-line no-restricted-globals\n    const nodeToRestoreRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(typeof document !== 'undefined' ? (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined).activeElement : null);\n    // restoring scopes should all track if they are active regardless of contain, but contain already tracks it plus logic to contain the focus\n    // restoring-non-containing scopes should only care if they become active so they can perform the restore\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        let scope = scopeRef.current;\n        const ownerDocument = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(scope ? scope[0] : undefined);\n        if (!restoreFocus || contain) return;\n        let onFocus = ()=>{\n            // If focusing an element in a child scope of the currently active scope, the child becomes active.\n            // Moving out of the active scope to an ancestor is not allowed.\n            if ((!$9bf71ea28793e738$var$activeScope || $9bf71ea28793e738$var$isAncestorScope($9bf71ea28793e738$var$activeScope, scopeRef)) && $9bf71ea28793e738$var$isElementInScope(ownerDocument.activeElement, scopeRef.current)) $9bf71ea28793e738$var$activeScope = scopeRef;\n        };\n        ownerDocument.addEventListener('focusin', onFocus, false);\n        scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.addEventListener('focusin', onFocus, false));\n        return ()=>{\n            ownerDocument.removeEventListener('focusin', onFocus, false);\n            scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.removeEventListener('focusin', onFocus, false));\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        scopeRef,\n        contain\n    ]);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        const ownerDocument = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined);\n        if (!restoreFocus) return;\n        // Handle the Tab key so that tabbing out of the scope goes to the next element\n        // after the node that had focus when the scope mounted. This is important when\n        // using portals for overlays, so that focus goes to the expected element when\n        // tabbing out of the overlay.\n        let onKeyDown = (e)=>{\n            if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !$9bf71ea28793e738$var$shouldContainFocus(scopeRef) || e.isComposing) return;\n            let focusedElement = ownerDocument.activeElement;\n            if (!$9bf71ea28793e738$var$isElementInChildScope(focusedElement, scopeRef) || !$9bf71ea28793e738$var$shouldRestoreFocus(scopeRef)) return;\n            let treeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef);\n            if (!treeNode) return;\n            let nodeToRestore = treeNode.nodeToRestore;\n            // Create a DOM tree walker that matches all tabbable elements\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(ownerDocument.body, {\n                tabbable: true\n            });\n            // Find the next tabbable element after the currently focused element\n            walker.currentNode = focusedElement;\n            let nextElement = e.shiftKey ? walker.previousNode() : walker.nextNode();\n            if (!nodeToRestore || !ownerDocument.body.contains(nodeToRestore) || nodeToRestore === ownerDocument.body) {\n                nodeToRestore = undefined;\n                treeNode.nodeToRestore = undefined;\n            }\n            // If there is no next element, or it is outside the current scope, move focus to the\n            // next element after the node to restore to instead.\n            if ((!nextElement || !$9bf71ea28793e738$var$isElementInChildScope(nextElement, scopeRef)) && nodeToRestore) {\n                walker.currentNode = nodeToRestore;\n                // Skip over elements within the scope, in case the scope immediately follows the node to restore.\n                do nextElement = e.shiftKey ? walker.previousNode() : walker.nextNode();\n                while ($9bf71ea28793e738$var$isElementInChildScope(nextElement, scopeRef));\n                e.preventDefault();\n                e.stopPropagation();\n                if (nextElement) $9bf71ea28793e738$var$focusElement(nextElement, true);\n                else // If there is no next element and the nodeToRestore isn't within a FocusScope (i.e. we are leaving the top level focus scope)\n                // then move focus to the body.\n                // Otherwise restore focus to the nodeToRestore (e.g menu within a popover -> tabbing to close the menu should move focus to menu trigger)\n                if (!$9bf71ea28793e738$var$isElementInAnyScope(nodeToRestore)) focusedElement.blur();\n                else $9bf71ea28793e738$var$focusElement(nodeToRestore, true);\n            }\n        };\n        if (!contain) ownerDocument.addEventListener('keydown', onKeyDown, true);\n        return ()=>{\n            if (!contain) ownerDocument.removeEventListener('keydown', onKeyDown, true);\n        };\n    }, [\n        scopeRef,\n        restoreFocus,\n        contain\n    ]);\n    // useLayoutEffect instead of useEffect so the active element is saved synchronously instead of asynchronously.\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        const ownerDocument = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined);\n        if (!restoreFocus) return;\n        let treeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef);\n        if (!treeNode) return;\n        var _nodeToRestoreRef_current;\n        treeNode.nodeToRestore = (_nodeToRestoreRef_current = nodeToRestoreRef.current) !== null && _nodeToRestoreRef_current !== void 0 ? _nodeToRestoreRef_current : undefined;\n        return ()=>{\n            let treeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef);\n            if (!treeNode) return;\n            let nodeToRestore = treeNode.nodeToRestore;\n            // if we already lost focus to the body and this was the active scope, then we should attempt to restore\n            if (restoreFocus && nodeToRestore && (ownerDocument.activeElement && $9bf71ea28793e738$var$isElementInChildScope(ownerDocument.activeElement, scopeRef) || ownerDocument.activeElement === ownerDocument.body && $9bf71ea28793e738$var$shouldRestoreFocus(scopeRef))) {\n                // freeze the focusScopeTree so it persists after the raf, otherwise during unmount nodes are removed from it\n                let clonedTree = $9bf71ea28793e738$export$d06fae2ee68b101e.clone();\n                requestAnimationFrame(()=>{\n                    // Only restore focus if we've lost focus to the body, the alternative is that focus has been purposefully moved elsewhere\n                    if (ownerDocument.activeElement === ownerDocument.body) {\n                        // look up the tree starting with our scope to find a nodeToRestore still in the DOM\n                        let treeNode = clonedTree.getTreeNode(scopeRef);\n                        while(treeNode){\n                            if (treeNode.nodeToRestore && treeNode.nodeToRestore.isConnected) {\n                                $9bf71ea28793e738$var$restoreFocusToElement(treeNode.nodeToRestore);\n                                return;\n                            }\n                            treeNode = treeNode.parent;\n                        }\n                        // If no nodeToRestore was found, focus the first element in the nearest\n                        // ancestor scope that is still in the tree.\n                        treeNode = clonedTree.getTreeNode(scopeRef);\n                        while(treeNode){\n                            if (treeNode.scopeRef && treeNode.scopeRef.current && $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(treeNode.scopeRef)) {\n                                let node = $9bf71ea28793e738$var$getFirstInScope(treeNode.scopeRef.current, true);\n                                $9bf71ea28793e738$var$restoreFocusToElement(node);\n                                return;\n                            }\n                            treeNode = treeNode.parent;\n                        }\n                    }\n                });\n            }\n        };\n    }, [\n        scopeRef,\n        restoreFocus\n    ]);\n}\nfunction $9bf71ea28793e738$var$restoreFocusToElement(node) {\n    // Dispatch a custom event that parent elements can intercept to customize focus restoration.\n    // For example, virtualized collection components reuse DOM elements, so the original element\n    // might still exist in the DOM but representing a different item.\n    if (node.dispatchEvent(new CustomEvent($9bf71ea28793e738$var$RESTORE_FOCUS_EVENT, {\n        bubbles: true,\n        cancelable: true\n    }))) $9bf71ea28793e738$var$focusElement(node);\n}\nfunction $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, opts, scope) {\n    let selector = (opts === null || opts === void 0 ? void 0 : opts.tabbable) ? $9bf71ea28793e738$var$TABBABLE_ELEMENT_SELECTOR : $9bf71ea28793e738$var$FOCUSABLE_ELEMENT_SELECTOR;\n    let walker = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(root).createTreeWalker(root, NodeFilter.SHOW_ELEMENT, {\n        acceptNode (node) {\n            var _opts_from;\n            // Skip nodes inside the starting node.\n            if (opts === null || opts === void 0 ? void 0 : (_opts_from = opts.from) === null || _opts_from === void 0 ? void 0 : _opts_from.contains(node)) return NodeFilter.FILTER_REJECT;\n            if (node.matches(selector) && (0, _isElementVisible_mjs__WEBPACK_IMPORTED_MODULE_6__.isElementVisible)(node) && (!scope || $9bf71ea28793e738$var$isElementInScope(node, scope)) && (!(opts === null || opts === void 0 ? void 0 : opts.accept) || opts.accept(node))) return NodeFilter.FILTER_ACCEPT;\n            return NodeFilter.FILTER_SKIP;\n        }\n    });\n    if (opts === null || opts === void 0 ? void 0 : opts.from) walker.currentNode = opts.from;\n    return walker;\n}\nfunction $9bf71ea28793e738$export$c5251b9e124bf29(ref, defaultOptions = {}) {\n    return {\n        focusNext (opts = {}) {\n            let root = ref.current;\n            if (!root) return null;\n            let { from: from, tabbable: tabbable = defaultOptions.tabbable, wrap: wrap = defaultOptions.wrap, accept: accept = defaultOptions.accept } = opts;\n            let node = from || (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(root).activeElement;\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, {\n                tabbable: tabbable,\n                accept: accept\n            });\n            if (root.contains(node)) walker.currentNode = node;\n            let nextNode = walker.nextNode();\n            if (!nextNode && wrap) {\n                walker.currentNode = root;\n                nextNode = walker.nextNode();\n            }\n            if (nextNode) $9bf71ea28793e738$var$focusElement(nextNode, true);\n            return nextNode;\n        },\n        focusPrevious (opts = defaultOptions) {\n            let root = ref.current;\n            if (!root) return null;\n            let { from: from, tabbable: tabbable = defaultOptions.tabbable, wrap: wrap = defaultOptions.wrap, accept: accept = defaultOptions.accept } = opts;\n            let node = from || (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(root).activeElement;\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, {\n                tabbable: tabbable,\n                accept: accept\n            });\n            if (root.contains(node)) walker.currentNode = node;\n            else {\n                let next = $9bf71ea28793e738$var$last(walker);\n                if (next) $9bf71ea28793e738$var$focusElement(next, true);\n                return next !== null && next !== void 0 ? next : null;\n            }\n            let previousNode = walker.previousNode();\n            if (!previousNode && wrap) {\n                walker.currentNode = root;\n                let lastNode = $9bf71ea28793e738$var$last(walker);\n                if (!lastNode) // couldn't wrap\n                return null;\n                previousNode = lastNode;\n            }\n            if (previousNode) $9bf71ea28793e738$var$focusElement(previousNode, true);\n            return previousNode !== null && previousNode !== void 0 ? previousNode : null;\n        },\n        focusFirst (opts = defaultOptions) {\n            let root = ref.current;\n            if (!root) return null;\n            let { tabbable: tabbable = defaultOptions.tabbable, accept: accept = defaultOptions.accept } = opts;\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, {\n                tabbable: tabbable,\n                accept: accept\n            });\n            let nextNode = walker.nextNode();\n            if (nextNode) $9bf71ea28793e738$var$focusElement(nextNode, true);\n            return nextNode;\n        },\n        focusLast (opts = defaultOptions) {\n            let root = ref.current;\n            if (!root) return null;\n            let { tabbable: tabbable = defaultOptions.tabbable, accept: accept = defaultOptions.accept } = opts;\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, {\n                tabbable: tabbable,\n                accept: accept\n            });\n            let next = $9bf71ea28793e738$var$last(walker);\n            if (next) $9bf71ea28793e738$var$focusElement(next, true);\n            return next !== null && next !== void 0 ? next : null;\n        }\n    };\n}\nfunction $9bf71ea28793e738$var$last(walker) {\n    let next = undefined;\n    let last;\n    do {\n        last = walker.lastChild();\n        if (last) next = last;\n    }while (last);\n    return next;\n}\nclass $9bf71ea28793e738$var$Tree {\n    get size() {\n        return this.fastMap.size;\n    }\n    getTreeNode(data) {\n        return this.fastMap.get(data);\n    }\n    addTreeNode(scopeRef, parent, nodeToRestore) {\n        let parentNode = this.fastMap.get(parent !== null && parent !== void 0 ? parent : null);\n        if (!parentNode) return;\n        let node = new $9bf71ea28793e738$var$TreeNode({\n            scopeRef: scopeRef\n        });\n        parentNode.addChild(node);\n        node.parent = parentNode;\n        this.fastMap.set(scopeRef, node);\n        if (nodeToRestore) node.nodeToRestore = nodeToRestore;\n    }\n    addNode(node) {\n        this.fastMap.set(node.scopeRef, node);\n    }\n    removeTreeNode(scopeRef) {\n        // never remove the root\n        if (scopeRef === null) return;\n        let node = this.fastMap.get(scopeRef);\n        if (!node) return;\n        let parentNode = node.parent;\n        // when we remove a scope, check if any sibling scopes are trying to restore focus to something inside the scope we're removing\n        // if we are, then replace the siblings restore with the restore from the scope we're removing\n        for (let current of this.traverse())if (current !== node && node.nodeToRestore && current.nodeToRestore && node.scopeRef && node.scopeRef.current && $9bf71ea28793e738$var$isElementInScope(current.nodeToRestore, node.scopeRef.current)) current.nodeToRestore = node.nodeToRestore;\n        let children = node.children;\n        if (parentNode) {\n            parentNode.removeChild(node);\n            if (children.size > 0) children.forEach((child)=>parentNode && parentNode.addChild(child));\n        }\n        this.fastMap.delete(node.scopeRef);\n    }\n    // Pre Order Depth First\n    *traverse(node = this.root) {\n        if (node.scopeRef != null) yield node;\n        if (node.children.size > 0) for (let child of node.children)yield* this.traverse(child);\n    }\n    clone() {\n        var _node_parent;\n        let newTree = new $9bf71ea28793e738$var$Tree();\n        var _node_parent_scopeRef;\n        for (let node of this.traverse())newTree.addTreeNode(node.scopeRef, (_node_parent_scopeRef = (_node_parent = node.parent) === null || _node_parent === void 0 ? void 0 : _node_parent.scopeRef) !== null && _node_parent_scopeRef !== void 0 ? _node_parent_scopeRef : null, node.nodeToRestore);\n        return newTree;\n    }\n    constructor(){\n        this.fastMap = new Map();\n        this.root = new $9bf71ea28793e738$var$TreeNode({\n            scopeRef: null\n        });\n        this.fastMap.set(null, this.root);\n    }\n}\nclass $9bf71ea28793e738$var$TreeNode {\n    addChild(node) {\n        this.children.add(node);\n        node.parent = this;\n    }\n    removeChild(node) {\n        this.children.delete(node);\n        node.parent = undefined;\n    }\n    constructor(props){\n        this.children = new Set();\n        this.contain = false;\n        this.scopeRef = props.scopeRef;\n    }\n}\nlet $9bf71ea28793e738$export$d06fae2ee68b101e = new $9bf71ea28793e738$var$Tree();\n\n\n\n//# sourceMappingURL=FocusScope.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/FocusScope.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/focusSafely.mjs":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/focusSafely.mjs ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusSafely: () => (/* binding */ $6a99195332edec8b$export$80f3e147d781571c)\n/* harmony export */ });\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/domHelpers.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/runAfterTransition.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/focusWithoutScrolling.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._697c5b52c6876ffd3e2260945ec4674e/node_modules/@react-aria/interactions/dist/useFocusVisible.mjs\");\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the 'License');\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an 'AS IS' BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\nfunction $6a99195332edec8b$export$80f3e147d781571c(element) {\n    // If the user is interacting with a virtual cursor, e.g. screen reader, then\n    // wait until after any animated transitions that are currently occurring on\n    // the page before shifting focus. This avoids issues with VoiceOver on iOS\n    // causing the page to scroll when moving focus if the element is transitioning\n    // from off the screen.\n    const ownerDocument = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.getOwnerDocument)(element);\n    if ((0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__.getInteractionModality)() === 'virtual') {\n        let lastFocusedElement = ownerDocument.activeElement;\n        (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.runAfterTransition)(()=>{\n            // If focus did not move and the element is still in the document, focus it.\n            if (ownerDocument.activeElement === lastFocusedElement && element.isConnected) (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.focusWithoutScrolling)(element);\n        });\n    } else (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.focusWithoutScrolling)(element);\n}\n\n\n\n//# sourceMappingURL=focusSafely.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/focusSafely.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/isElementVisible.mjs":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/isElementVisible.mjs ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isElementVisible: () => (/* binding */ $645f2e67b85a24c9$export$e989c0fffaa6b27a)\n/* harmony export */ });\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/domHelpers.mjs\");\n\n\n/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \nfunction $645f2e67b85a24c9$var$isStyleVisible(element) {\n    const windowObject = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.getOwnerWindow)(element);\n    if (!(element instanceof windowObject.HTMLElement) && !(element instanceof windowObject.SVGElement)) return false;\n    let { display: display, visibility: visibility } = element.style;\n    let isVisible = display !== 'none' && visibility !== 'hidden' && visibility !== 'collapse';\n    if (isVisible) {\n        const { getComputedStyle: getComputedStyle } = element.ownerDocument.defaultView;\n        let { display: computedDisplay, visibility: computedVisibility } = getComputedStyle(element);\n        isVisible = computedDisplay !== 'none' && computedVisibility !== 'hidden' && computedVisibility !== 'collapse';\n    }\n    return isVisible;\n}\nfunction $645f2e67b85a24c9$var$isAttributeVisible(element, childElement) {\n    return !element.hasAttribute('hidden') && // Ignore HiddenSelect when tree walking.\n    !element.hasAttribute('data-react-aria-prevent-focus') && (element.nodeName === 'DETAILS' && childElement && childElement.nodeName !== 'SUMMARY' ? element.hasAttribute('open') : true);\n}\nfunction $645f2e67b85a24c9$export$e989c0fffaa6b27a(element, childElement) {\n    return element.nodeName !== '#comment' && $645f2e67b85a24c9$var$isStyleVisible(element) && $645f2e67b85a24c9$var$isAttributeVisible(element, childElement) && (!element.parentElement || $645f2e67b85a24c9$export$e989c0fffaa6b27a(element.parentElement, element));\n}\n\n\n\n//# sourceMappingURL=isElementVisible.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/isElementVisible.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/useFocusRing.mjs":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/useFocusRing.mjs ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFocusRing: () => (/* binding */ $f7dceffc5ad7768b$export$4e328f61c538687f)\n/* harmony export */ });\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._697c5b52c6876ffd3e2260945ec4674e/node_modules/@react-aria/interactions/dist/useFocusVisible.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._697c5b52c6876ffd3e2260945ec4674e/node_modules/@react-aria/interactions/dist/useFocus.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._697c5b52c6876ffd3e2260945ec4674e/node_modules/@react-aria/interactions/dist/useFocusWithin.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\nfunction $f7dceffc5ad7768b$export$4e328f61c538687f(props = {}) {\n    let { autoFocus: autoFocus = false, isTextInput: isTextInput, within: within } = props;\n    let state = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        isFocused: false,\n        isFocusVisible: autoFocus || (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__.isFocusVisible)()\n    });\n    let [isFocused, setFocused] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    let [isFocusVisibleState, setFocusVisible] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>state.current.isFocused && state.current.isFocusVisible);\n    let updateState = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>setFocusVisible(state.current.isFocused && state.current.isFocusVisible), []);\n    let onFocusChange = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((isFocused)=>{\n        state.current.isFocused = isFocused;\n        setFocused(isFocused);\n        updateState();\n    }, [\n        updateState\n    ]);\n    (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__.useFocusVisibleListener)((isFocusVisible)=>{\n        state.current.isFocusVisible = isFocusVisible;\n        updateState();\n    }, [], {\n        isTextInput: isTextInput\n    });\n    let { focusProps: focusProps } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_2__.useFocus)({\n        isDisabled: within,\n        onFocusChange: onFocusChange\n    });\n    let { focusWithinProps: focusWithinProps } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_3__.useFocusWithin)({\n        isDisabled: !within,\n        onFocusWithinChange: onFocusChange\n    });\n    return {\n        isFocused: isFocused,\n        isFocusVisible: isFocusVisibleState,\n        focusProps: within ? focusWithinProps : focusProps\n    };\n}\n\n\n\n//# sourceMappingURL=useFocusRing.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/useFocusRing.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/useFocusable.mjs":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/useFocusable.mjs ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusableProvider: () => (/* binding */ $e6afbd83fe6ebbd2$export$13f3202a3e5ddd5),\n/* harmony export */   useFocusable: () => (/* binding */ $e6afbd83fe6ebbd2$export$4c014de7c8940b4c)\n/* harmony export */ });\n/* harmony import */ var _focusSafely_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./focusSafely.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/focusSafely.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/useSyncRef.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/useObjectRef.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/mergeProps.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._697c5b52c6876ffd3e2260945ec4674e/node_modules/@react-aria/interactions/dist/useFocus.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._697c5b52c6876ffd3e2260945ec4674e/node_modules/@react-aria/interactions/dist/useKeyboard.mjs\");\n\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n\nlet $e6afbd83fe6ebbd2$var$FocusableContext = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createContext(null);\nfunction $e6afbd83fe6ebbd2$var$useFocusableContext(ref) {\n    let context = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($e6afbd83fe6ebbd2$var$FocusableContext) || {};\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useSyncRef)(context, ref);\n    // eslint-disable-next-line\n    let { ref: _, ...otherProps } = context;\n    return otherProps;\n}\nconst $e6afbd83fe6ebbd2$export$13f3202a3e5ddd5 = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).forwardRef(function FocusableProvider(props, ref) {\n    let { children: children, ...otherProps } = props;\n    let objRef = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.useObjectRef)(ref);\n    let context = {\n        ...otherProps,\n        ref: objRef\n    };\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($e6afbd83fe6ebbd2$var$FocusableContext.Provider, {\n        value: context\n    }, children);\n});\nfunction $e6afbd83fe6ebbd2$export$4c014de7c8940b4c(props, domRef) {\n    let { focusProps: focusProps } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_3__.useFocus)(props);\n    let { keyboardProps: keyboardProps } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_4__.useKeyboard)(props);\n    let interactions = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.mergeProps)(focusProps, keyboardProps);\n    let domProps = $e6afbd83fe6ebbd2$var$useFocusableContext(domRef);\n    let interactionProps = props.isDisabled ? {} : domProps;\n    let autoFocusRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(props.autoFocus);\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (autoFocusRef.current && domRef.current) (0, _focusSafely_mjs__WEBPACK_IMPORTED_MODULE_6__.focusSafely)(domRef.current);\n        autoFocusRef.current = false;\n    }, [\n        domRef\n    ]);\n    return {\n        focusableProps: (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.mergeProps)({\n            ...interactions,\n            tabIndex: props.excludeFromTabOrder && !props.isDisabled ? -1 : undefined\n        }, interactionProps)\n    };\n}\n\n\n\n//# sourceMappingURL=useFocusable.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+focus@3.19.1_re_f22b377d4e01f6fec11051c4684fbd09/node_modules/@react-aria/focus/dist/useFocusable.mjs\n");

/***/ })

};
;