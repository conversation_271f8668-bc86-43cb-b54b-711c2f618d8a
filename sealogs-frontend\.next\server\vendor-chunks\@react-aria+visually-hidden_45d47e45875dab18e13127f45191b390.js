"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+visually-hidden_45d47e45875dab18e13127f45191b390";
exports.ids = ["vendor-chunks/@react-aria+visually-hidden_45d47e45875dab18e13127f45191b390"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+visually-hidden_45d47e45875dab18e13127f45191b390/node_modules/@react-aria/visually-hidden/dist/VisuallyHidden.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+visually-hidden_45d47e45875dab18e13127f45191b390/node_modules/@react-aria/visually-hidden/dist/VisuallyHidden.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VisuallyHidden: () => (/* binding */ $5c3e21d68f1c4674$export$439d29a4e110a164),\n/* harmony export */   useVisuallyHidden: () => (/* binding */ $5c3e21d68f1c4674$export$a966af930f325cab)\n/* harmony export */ });\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/mergeProps.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._697c5b52c6876ffd3e2260945ec4674e/node_modules/@react-aria/interactions/dist/useFocusWithin.mjs\");\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\nconst $5c3e21d68f1c4674$var$styles = {\n    border: 0,\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(50%)',\n    height: '1px',\n    margin: '-1px',\n    overflow: 'hidden',\n    padding: 0,\n    position: 'absolute',\n    width: '1px',\n    whiteSpace: 'nowrap'\n};\nfunction $5c3e21d68f1c4674$export$a966af930f325cab(props = {}) {\n    let { style: style, isFocusable: isFocusable } = props;\n    let [isFocused, setFocused] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    let { focusWithinProps: focusWithinProps } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__.useFocusWithin)({\n        isDisabled: !isFocusable,\n        onFocusWithinChange: (val)=>setFocused(val)\n    });\n    // If focused, don't hide the element.\n    let combinedStyles = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (isFocused) return style;\n        else if (style) return {\n            ...$5c3e21d68f1c4674$var$styles,\n            ...style\n        };\n        else return $5c3e21d68f1c4674$var$styles;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isFocused\n    ]);\n    return {\n        visuallyHiddenProps: {\n            ...focusWithinProps,\n            style: combinedStyles\n        }\n    };\n}\nfunction $5c3e21d68f1c4674$export$439d29a4e110a164(props) {\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    let { children: children, elementType: Element = 'div', isFocusable: isFocusable, style: style, ...otherProps } = props;\n    let { visuallyHiddenProps: visuallyHiddenProps } = $5c3e21d68f1c4674$export$a966af930f325cab(props);\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement(Element, (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.mergeProps)(otherProps, visuallyHiddenProps), children);\n}\n\n\n\n//# sourceMappingURL=VisuallyHidden.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+visually-hidden_45d47e45875dab18e13127f45191b390/node_modules/@react-aria/visually-hidden/dist/VisuallyHidden.mjs\n");

/***/ })

};
;