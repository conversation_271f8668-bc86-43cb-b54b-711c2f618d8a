"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-stately+selection@3.19.0_react@18.3.1";
exports.ids = ["vendor-chunks/@react-stately+selection@3.19.0_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+selection@3.19.0_react@18.3.1/node_modules/@react-stately/selection/dist/Selection.mjs":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+selection@3.19.0_react@18.3.1/node_modules/@react-stately/selection/dist/Selection.mjs ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Selection: () => (/* binding */ $e40ea825a81a3709$export$52baac22726c72bf)\n/* harmony export */ });\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ class $e40ea825a81a3709$export$52baac22726c72bf extends Set {\n    constructor(keys, anchorKey, currentKey){\n        super(keys);\n        if (keys instanceof $e40ea825a81a3709$export$52baac22726c72bf) {\n            this.anchorKey = anchorKey !== null && anchorKey !== void 0 ? anchorKey : keys.anchorKey;\n            this.currentKey = currentKey !== null && currentKey !== void 0 ? currentKey : keys.currentKey;\n        } else {\n            this.anchorKey = anchorKey !== null && anchorKey !== void 0 ? anchorKey : null;\n            this.currentKey = currentKey !== null && currentKey !== void 0 ? currentKey : null;\n        }\n    }\n}\n\n\n\n//# sourceMappingURL=Selection.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXN0YXRlbHkrc2VsZWN0aW9uQDMuMTkuMF9yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL0ByZWFjdC1zdGF0ZWx5L3NlbGVjdGlvbi9kaXN0L1NlbGVjdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUdnRTtBQUNoRSIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXN0YXRlbHkrc2VsZWN0aW9uQDMuMTkuMF9yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL0ByZWFjdC1zdGF0ZWx5L3NlbGVjdGlvbi9kaXN0L1NlbGVjdGlvbi5tanM/OGU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogQ29weXJpZ2h0IDIwMjAgQWRvYmUuIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4gKiBUaGlzIGZpbGUgaXMgbGljZW5zZWQgdG8geW91IHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuIFlvdSBtYXkgb2J0YWluIGEgY29weVxuICogb2YgdGhlIExpY2Vuc2UgYXQgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZSBkaXN0cmlidXRlZCB1bmRlclxuICogdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLCBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgUkVQUkVTRU5UQVRJT05TXG4gKiBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC4gU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2VcbiAqIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmQgbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi8gY2xhc3MgJGU0MGVhODI1YTgxYTM3MDkkZXhwb3J0JDUyYmFhYzIyNzI2YzcyYmYgZXh0ZW5kcyBTZXQge1xuICAgIGNvbnN0cnVjdG9yKGtleXMsIGFuY2hvcktleSwgY3VycmVudEtleSl7XG4gICAgICAgIHN1cGVyKGtleXMpO1xuICAgICAgICBpZiAoa2V5cyBpbnN0YW5jZW9mICRlNDBlYTgyNWE4MWEzNzA5JGV4cG9ydCQ1MmJhYWMyMjcyNmM3MmJmKSB7XG4gICAgICAgICAgICB0aGlzLmFuY2hvcktleSA9IGFuY2hvcktleSAhPT0gbnVsbCAmJiBhbmNob3JLZXkgIT09IHZvaWQgMCA/IGFuY2hvcktleSA6IGtleXMuYW5jaG9yS2V5O1xuICAgICAgICAgICAgdGhpcy5jdXJyZW50S2V5ID0gY3VycmVudEtleSAhPT0gbnVsbCAmJiBjdXJyZW50S2V5ICE9PSB2b2lkIDAgPyBjdXJyZW50S2V5IDoga2V5cy5jdXJyZW50S2V5O1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5hbmNob3JLZXkgPSBhbmNob3JLZXkgIT09IG51bGwgJiYgYW5jaG9yS2V5ICE9PSB2b2lkIDAgPyBhbmNob3JLZXkgOiBudWxsO1xuICAgICAgICAgICAgdGhpcy5jdXJyZW50S2V5ID0gY3VycmVudEtleSAhPT0gbnVsbCAmJiBjdXJyZW50S2V5ICE9PSB2b2lkIDAgPyBjdXJyZW50S2V5IDogbnVsbDtcbiAgICAgICAgfVxuICAgIH1cbn1cblxuXG5leHBvcnQgeyRlNDBlYTgyNWE4MWEzNzA5JGV4cG9ydCQ1MmJhYWMyMjcyNmM3MmJmIGFzIFNlbGVjdGlvbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1TZWxlY3Rpb24ubW9kdWxlLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+selection@3.19.0_react@18.3.1/node_modules/@react-stately/selection/dist/Selection.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+selection@3.19.0_react@18.3.1/node_modules/@react-stately/selection/dist/SelectionManager.mjs":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+selection@3.19.0_react@18.3.1/node_modules/@react-stately/selection/dist/SelectionManager.mjs ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SelectionManager: () => (/* binding */ $d496c0a20b6e58ec$export$6c8a5aaad13c9852)\n/* harmony export */ });\n/* harmony import */ var _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Selection.mjs */ \"(ssr)/./node_modules/.pnpm/@react-stately+selection@3.19.0_react@18.3.1/node_modules/@react-stately/selection/dist/Selection.mjs\");\n/* harmony import */ var _react_stately_collections__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-stately/collections */ \"(ssr)/./node_modules/.pnpm/@react-stately+collections@3.12.1_react@18.3.1/node_modules/@react-stately/collections/dist/getChildNodes.mjs\");\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\nclass $d496c0a20b6e58ec$export$6c8a5aaad13c9852 {\n    /**\n   * The type of selection that is allowed in the collection.\n   */ get selectionMode() {\n        return this.state.selectionMode;\n    }\n    /**\n   * Whether the collection allows empty selection.\n   */ get disallowEmptySelection() {\n        return this.state.disallowEmptySelection;\n    }\n    /**\n   * The selection behavior for the collection.\n   */ get selectionBehavior() {\n        return this.state.selectionBehavior;\n    }\n    /**\n   * Sets the selection behavior for the collection.\n   */ setSelectionBehavior(selectionBehavior) {\n        this.state.setSelectionBehavior(selectionBehavior);\n    }\n    /**\n   * Whether the collection is currently focused.\n   */ get isFocused() {\n        return this.state.isFocused;\n    }\n    /**\n   * Sets whether the collection is focused.\n   */ setFocused(isFocused) {\n        this.state.setFocused(isFocused);\n    }\n    /**\n   * The current focused key in the collection.\n   */ get focusedKey() {\n        return this.state.focusedKey;\n    }\n    /** Whether the first or last child of the focused key should receive focus. */ get childFocusStrategy() {\n        return this.state.childFocusStrategy;\n    }\n    /**\n   * Sets the focused key.\n   */ setFocusedKey(key, childFocusStrategy) {\n        if (key == null || this.collection.getItem(key)) this.state.setFocusedKey(key, childFocusStrategy);\n    }\n    /**\n   * The currently selected keys in the collection.\n   */ get selectedKeys() {\n        return this.state.selectedKeys === 'all' ? new Set(this.getSelectAllKeys()) : this.state.selectedKeys;\n    }\n    /**\n   * The raw selection value for the collection.\n   * Either 'all' for select all, or a set of keys.\n   */ get rawSelection() {\n        return this.state.selectedKeys;\n    }\n    /**\n   * Returns whether a key is selected.\n   */ isSelected(key) {\n        if (this.state.selectionMode === 'none') return false;\n        let mappedKey = this.getKey(key);\n        if (mappedKey == null) return false;\n        return this.state.selectedKeys === 'all' ? this.canSelectItem(mappedKey) : this.state.selectedKeys.has(mappedKey);\n    }\n    /**\n   * Whether the selection is empty.\n   */ get isEmpty() {\n        return this.state.selectedKeys !== 'all' && this.state.selectedKeys.size === 0;\n    }\n    /**\n   * Whether all items in the collection are selected.\n   */ get isSelectAll() {\n        if (this.isEmpty) return false;\n        if (this.state.selectedKeys === 'all') return true;\n        if (this._isSelectAll != null) return this._isSelectAll;\n        let allKeys = this.getSelectAllKeys();\n        let selectedKeys = this.state.selectedKeys;\n        this._isSelectAll = allKeys.every((k)=>selectedKeys.has(k));\n        return this._isSelectAll;\n    }\n    get firstSelectedKey() {\n        let first = null;\n        for (let key of this.state.selectedKeys){\n            let item = this.collection.getItem(key);\n            if (!first || item && (0, _react_stately_collections__WEBPACK_IMPORTED_MODULE_0__.compareNodeOrder)(this.collection, item, first) < 0) first = item;\n        }\n        var _first_key;\n        return (_first_key = first === null || first === void 0 ? void 0 : first.key) !== null && _first_key !== void 0 ? _first_key : null;\n    }\n    get lastSelectedKey() {\n        let last = null;\n        for (let key of this.state.selectedKeys){\n            let item = this.collection.getItem(key);\n            if (!last || item && (0, _react_stately_collections__WEBPACK_IMPORTED_MODULE_0__.compareNodeOrder)(this.collection, item, last) > 0) last = item;\n        }\n        var _last_key;\n        return (_last_key = last === null || last === void 0 ? void 0 : last.key) !== null && _last_key !== void 0 ? _last_key : null;\n    }\n    get disabledKeys() {\n        return this.state.disabledKeys;\n    }\n    get disabledBehavior() {\n        return this.state.disabledBehavior;\n    }\n    /**\n   * Extends the selection to the given key.\n   */ extendSelection(toKey) {\n        if (this.selectionMode === 'none') return;\n        if (this.selectionMode === 'single') {\n            this.replaceSelection(toKey);\n            return;\n        }\n        let mappedToKey = this.getKey(toKey);\n        if (mappedToKey == null) return;\n        let selection;\n        // Only select the one key if coming from a select all.\n        if (this.state.selectedKeys === 'all') selection = new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)([\n            mappedToKey\n        ], mappedToKey, mappedToKey);\n        else {\n            let selectedKeys = this.state.selectedKeys;\n            var _selectedKeys_anchorKey;\n            let anchorKey = (_selectedKeys_anchorKey = selectedKeys.anchorKey) !== null && _selectedKeys_anchorKey !== void 0 ? _selectedKeys_anchorKey : mappedToKey;\n            selection = new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)(selectedKeys, anchorKey, mappedToKey);\n            var _selectedKeys_currentKey;\n            for (let key of this.getKeyRange(anchorKey, (_selectedKeys_currentKey = selectedKeys.currentKey) !== null && _selectedKeys_currentKey !== void 0 ? _selectedKeys_currentKey : mappedToKey))selection.delete(key);\n            for (let key of this.getKeyRange(mappedToKey, anchorKey))if (this.canSelectItem(key)) selection.add(key);\n        }\n        this.state.setSelectedKeys(selection);\n    }\n    getKeyRange(from, to) {\n        let fromItem = this.collection.getItem(from);\n        let toItem = this.collection.getItem(to);\n        if (fromItem && toItem) {\n            if ((0, _react_stately_collections__WEBPACK_IMPORTED_MODULE_0__.compareNodeOrder)(this.collection, fromItem, toItem) <= 0) return this.getKeyRangeInternal(from, to);\n            return this.getKeyRangeInternal(to, from);\n        }\n        return [];\n    }\n    getKeyRangeInternal(from, to) {\n        var _this_layoutDelegate;\n        if ((_this_layoutDelegate = this.layoutDelegate) === null || _this_layoutDelegate === void 0 ? void 0 : _this_layoutDelegate.getKeyRange) return this.layoutDelegate.getKeyRange(from, to);\n        let keys = [];\n        let key = from;\n        while(key != null){\n            let item = this.collection.getItem(key);\n            if (item && (item.type === 'item' || item.type === 'cell' && this.allowsCellSelection)) keys.push(key);\n            if (key === to) return keys;\n            key = this.collection.getKeyAfter(key);\n        }\n        return [];\n    }\n    getKey(key) {\n        let item = this.collection.getItem(key);\n        if (!item) // ¯\\_(ツ)_/¯\n        return key;\n        // If cell selection is allowed, just return the key.\n        if (item.type === 'cell' && this.allowsCellSelection) return key;\n        // Find a parent item to select\n        while(item && item.type !== 'item' && item.parentKey != null)item = this.collection.getItem(item.parentKey);\n        if (!item || item.type !== 'item') return null;\n        return item.key;\n    }\n    /**\n   * Toggles whether the given key is selected.\n   */ toggleSelection(key) {\n        if (this.selectionMode === 'none') return;\n        if (this.selectionMode === 'single' && !this.isSelected(key)) {\n            this.replaceSelection(key);\n            return;\n        }\n        let mappedKey = this.getKey(key);\n        if (mappedKey == null) return;\n        let keys = new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)(this.state.selectedKeys === 'all' ? this.getSelectAllKeys() : this.state.selectedKeys);\n        if (keys.has(mappedKey)) keys.delete(mappedKey);\n        else if (this.canSelectItem(mappedKey)) {\n            keys.add(mappedKey);\n            keys.anchorKey = mappedKey;\n            keys.currentKey = mappedKey;\n        }\n        if (this.disallowEmptySelection && keys.size === 0) return;\n        this.state.setSelectedKeys(keys);\n    }\n    /**\n   * Replaces the selection with only the given key.\n   */ replaceSelection(key) {\n        if (this.selectionMode === 'none') return;\n        let mappedKey = this.getKey(key);\n        if (mappedKey == null) return;\n        let selection = this.canSelectItem(mappedKey) ? new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)([\n            mappedKey\n        ], mappedKey, mappedKey) : new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)();\n        this.state.setSelectedKeys(selection);\n    }\n    /**\n   * Replaces the selection with the given keys.\n   */ setSelectedKeys(keys) {\n        if (this.selectionMode === 'none') return;\n        let selection = new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)();\n        for (let key of keys){\n            let mappedKey = this.getKey(key);\n            if (mappedKey != null) {\n                selection.add(mappedKey);\n                if (this.selectionMode === 'single') break;\n            }\n        }\n        this.state.setSelectedKeys(selection);\n    }\n    getSelectAllKeys() {\n        let keys = [];\n        let addKeys = (key)=>{\n            while(key != null){\n                if (this.canSelectItem(key)) {\n                    var _getFirstItem;\n                    let item = this.collection.getItem(key);\n                    if ((item === null || item === void 0 ? void 0 : item.type) === 'item') keys.push(key);\n                    var _getFirstItem_key;\n                    // Add child keys. If cell selection is allowed, then include item children too.\n                    if ((item === null || item === void 0 ? void 0 : item.hasChildNodes) && (this.allowsCellSelection || item.type !== 'item')) addKeys((_getFirstItem_key = (_getFirstItem = (0, _react_stately_collections__WEBPACK_IMPORTED_MODULE_0__.getFirstItem)((0, _react_stately_collections__WEBPACK_IMPORTED_MODULE_0__.getChildNodes)(item, this.collection))) === null || _getFirstItem === void 0 ? void 0 : _getFirstItem.key) !== null && _getFirstItem_key !== void 0 ? _getFirstItem_key : null);\n                }\n                key = this.collection.getKeyAfter(key);\n            }\n        };\n        addKeys(this.collection.getFirstKey());\n        return keys;\n    }\n    /**\n   * Selects all items in the collection.\n   */ selectAll() {\n        if (!this.isSelectAll && this.selectionMode === 'multiple') this.state.setSelectedKeys('all');\n    }\n    /**\n   * Removes all keys from the selection.\n   */ clearSelection() {\n        if (!this.disallowEmptySelection && (this.state.selectedKeys === 'all' || this.state.selectedKeys.size > 0)) this.state.setSelectedKeys(new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)());\n    }\n    /**\n   * Toggles between select all and an empty selection.\n   */ toggleSelectAll() {\n        if (this.isSelectAll) this.clearSelection();\n        else this.selectAll();\n    }\n    select(key, e) {\n        if (this.selectionMode === 'none') return;\n        if (this.selectionMode === 'single') {\n            if (this.isSelected(key) && !this.disallowEmptySelection) this.toggleSelection(key);\n            else this.replaceSelection(key);\n        } else if (this.selectionBehavior === 'toggle' || e && (e.pointerType === 'touch' || e.pointerType === 'virtual')) // if touch or virtual (VO) then we just want to toggle, otherwise it's impossible to multi select because they don't have modifier keys\n        this.toggleSelection(key);\n        else this.replaceSelection(key);\n    }\n    /**\n   * Returns whether the current selection is equal to the given selection.\n   */ isSelectionEqual(selection) {\n        if (selection === this.state.selectedKeys) return true;\n        // Check if the set of keys match.\n        let selectedKeys = this.selectedKeys;\n        if (selection.size !== selectedKeys.size) return false;\n        for (let key of selection){\n            if (!selectedKeys.has(key)) return false;\n        }\n        for (let key of selectedKeys){\n            if (!selection.has(key)) return false;\n        }\n        return true;\n    }\n    canSelectItem(key) {\n        var _item_props;\n        if (this.state.selectionMode === 'none' || this.state.disabledKeys.has(key)) return false;\n        let item = this.collection.getItem(key);\n        if (!item || (item === null || item === void 0 ? void 0 : (_item_props = item.props) === null || _item_props === void 0 ? void 0 : _item_props.isDisabled) || item.type === 'cell' && !this.allowsCellSelection) return false;\n        return true;\n    }\n    isDisabled(key) {\n        var _this_collection_getItem_props, _this_collection_getItem;\n        return this.state.disabledBehavior === 'all' && (this.state.disabledKeys.has(key) || !!((_this_collection_getItem = this.collection.getItem(key)) === null || _this_collection_getItem === void 0 ? void 0 : (_this_collection_getItem_props = _this_collection_getItem.props) === null || _this_collection_getItem_props === void 0 ? void 0 : _this_collection_getItem_props.isDisabled));\n    }\n    isLink(key) {\n        var _this_collection_getItem_props, _this_collection_getItem;\n        return !!((_this_collection_getItem = this.collection.getItem(key)) === null || _this_collection_getItem === void 0 ? void 0 : (_this_collection_getItem_props = _this_collection_getItem.props) === null || _this_collection_getItem_props === void 0 ? void 0 : _this_collection_getItem_props.href);\n    }\n    getItemProps(key) {\n        var _this_collection_getItem;\n        return (_this_collection_getItem = this.collection.getItem(key)) === null || _this_collection_getItem === void 0 ? void 0 : _this_collection_getItem.props;\n    }\n    constructor(collection, state, options){\n        this.collection = collection;\n        this.state = state;\n        var _options_allowsCellSelection;\n        this.allowsCellSelection = (_options_allowsCellSelection = options === null || options === void 0 ? void 0 : options.allowsCellSelection) !== null && _options_allowsCellSelection !== void 0 ? _options_allowsCellSelection : false;\n        this._isSelectAll = null;\n        this.layoutDelegate = (options === null || options === void 0 ? void 0 : options.layoutDelegate) || null;\n    }\n}\n\n\n\n//# sourceMappingURL=SelectionManager.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXN0YXRlbHkrc2VsZWN0aW9uQDMuMTkuMF9yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL0ByZWFjdC1zdGF0ZWx5L3NlbGVjdGlvbi9kaXN0L1NlbGVjdGlvbk1hbmFnZXIubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF1RjtBQUM0RTs7QUFFbks7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLHdFQUF1QjtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLHdFQUF1QjtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1FQUFtRSxxREFBeUM7QUFDNUc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLHFEQUF5QztBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQix3RUFBdUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixxREFBeUM7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0VBQWdFLHFEQUF5QztBQUN6RztBQUNBLDJDQUEyQyxxREFBeUM7QUFDcEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLHFEQUF5QztBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtNQUFrTSxvRUFBbUIsTUFBTSxxRUFBb0I7QUFDL087QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0pBQXdKLHFEQUF5QztBQUNqTTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUd1RTtBQUN2RSIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXN0YXRlbHkrc2VsZWN0aW9uQDMuMTkuMF9yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL0ByZWFjdC1zdGF0ZWx5L3NlbGVjdGlvbi9kaXN0L1NlbGVjdGlvbk1hbmFnZXIubWpzP2ZkNTIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtTZWxlY3Rpb24gYXMgJGU0MGVhODI1YTgxYTM3MDkkZXhwb3J0JDUyYmFhYzIyNzI2YzcyYmZ9IGZyb20gXCIuL1NlbGVjdGlvbi5tanNcIjtcbmltcG9ydCB7Y29tcGFyZU5vZGVPcmRlciBhcyAkamtoVVQkY29tcGFyZU5vZGVPcmRlciwgZ2V0Rmlyc3RJdGVtIGFzICRqa2hVVCRnZXRGaXJzdEl0ZW0sIGdldENoaWxkTm9kZXMgYXMgJGpraFVUJGdldENoaWxkTm9kZXN9IGZyb20gXCJAcmVhY3Qtc3RhdGVseS9jb2xsZWN0aW9uc1wiO1xuXG4vKlxuICogQ29weXJpZ2h0IDIwMjAgQWRvYmUuIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4gKiBUaGlzIGZpbGUgaXMgbGljZW5zZWQgdG8geW91IHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuIFlvdSBtYXkgb2J0YWluIGEgY29weVxuICogb2YgdGhlIExpY2Vuc2UgYXQgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZSBkaXN0cmlidXRlZCB1bmRlclxuICogdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLCBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgUkVQUkVTRU5UQVRJT05TXG4gKiBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC4gU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2VcbiAqIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmQgbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi8gXG5cbmNsYXNzICRkNDk2YzBhMjBiNmU1OGVjJGV4cG9ydCQ2YzhhNWFhYWQxM2M5ODUyIHtcbiAgICAvKipcbiAgICogVGhlIHR5cGUgb2Ygc2VsZWN0aW9uIHRoYXQgaXMgYWxsb3dlZCBpbiB0aGUgY29sbGVjdGlvbi5cbiAgICovIGdldCBzZWxlY3Rpb25Nb2RlKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5zdGF0ZS5zZWxlY3Rpb25Nb2RlO1xuICAgIH1cbiAgICAvKipcbiAgICogV2hldGhlciB0aGUgY29sbGVjdGlvbiBhbGxvd3MgZW1wdHkgc2VsZWN0aW9uLlxuICAgKi8gZ2V0IGRpc2FsbG93RW1wdHlTZWxlY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLmRpc2FsbG93RW1wdHlTZWxlY3Rpb247XG4gICAgfVxuICAgIC8qKlxuICAgKiBUaGUgc2VsZWN0aW9uIGJlaGF2aW9yIGZvciB0aGUgY29sbGVjdGlvbi5cbiAgICovIGdldCBzZWxlY3Rpb25CZWhhdmlvcigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RhdGUuc2VsZWN0aW9uQmVoYXZpb3I7XG4gICAgfVxuICAgIC8qKlxuICAgKiBTZXRzIHRoZSBzZWxlY3Rpb24gYmVoYXZpb3IgZm9yIHRoZSBjb2xsZWN0aW9uLlxuICAgKi8gc2V0U2VsZWN0aW9uQmVoYXZpb3Ioc2VsZWN0aW9uQmVoYXZpb3IpIHtcbiAgICAgICAgdGhpcy5zdGF0ZS5zZXRTZWxlY3Rpb25CZWhhdmlvcihzZWxlY3Rpb25CZWhhdmlvcik7XG4gICAgfVxuICAgIC8qKlxuICAgKiBXaGV0aGVyIHRoZSBjb2xsZWN0aW9uIGlzIGN1cnJlbnRseSBmb2N1c2VkLlxuICAgKi8gZ2V0IGlzRm9jdXNlZCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RhdGUuaXNGb2N1c2VkO1xuICAgIH1cbiAgICAvKipcbiAgICogU2V0cyB3aGV0aGVyIHRoZSBjb2xsZWN0aW9uIGlzIGZvY3VzZWQuXG4gICAqLyBzZXRGb2N1c2VkKGlzRm9jdXNlZCkge1xuICAgICAgICB0aGlzLnN0YXRlLnNldEZvY3VzZWQoaXNGb2N1c2VkKTtcbiAgICB9XG4gICAgLyoqXG4gICAqIFRoZSBjdXJyZW50IGZvY3VzZWQga2V5IGluIHRoZSBjb2xsZWN0aW9uLlxuICAgKi8gZ2V0IGZvY3VzZWRLZXkoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLmZvY3VzZWRLZXk7XG4gICAgfVxuICAgIC8qKiBXaGV0aGVyIHRoZSBmaXJzdCBvciBsYXN0IGNoaWxkIG9mIHRoZSBmb2N1c2VkIGtleSBzaG91bGQgcmVjZWl2ZSBmb2N1cy4gKi8gZ2V0IGNoaWxkRm9jdXNTdHJhdGVneSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RhdGUuY2hpbGRGb2N1c1N0cmF0ZWd5O1xuICAgIH1cbiAgICAvKipcbiAgICogU2V0cyB0aGUgZm9jdXNlZCBrZXkuXG4gICAqLyBzZXRGb2N1c2VkS2V5KGtleSwgY2hpbGRGb2N1c1N0cmF0ZWd5KSB7XG4gICAgICAgIGlmIChrZXkgPT0gbnVsbCB8fCB0aGlzLmNvbGxlY3Rpb24uZ2V0SXRlbShrZXkpKSB0aGlzLnN0YXRlLnNldEZvY3VzZWRLZXkoa2V5LCBjaGlsZEZvY3VzU3RyYXRlZ3kpO1xuICAgIH1cbiAgICAvKipcbiAgICogVGhlIGN1cnJlbnRseSBzZWxlY3RlZCBrZXlzIGluIHRoZSBjb2xsZWN0aW9uLlxuICAgKi8gZ2V0IHNlbGVjdGVkS2V5cygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RhdGUuc2VsZWN0ZWRLZXlzID09PSAnYWxsJyA/IG5ldyBTZXQodGhpcy5nZXRTZWxlY3RBbGxLZXlzKCkpIDogdGhpcy5zdGF0ZS5zZWxlY3RlZEtleXM7XG4gICAgfVxuICAgIC8qKlxuICAgKiBUaGUgcmF3IHNlbGVjdGlvbiB2YWx1ZSBmb3IgdGhlIGNvbGxlY3Rpb24uXG4gICAqIEVpdGhlciAnYWxsJyBmb3Igc2VsZWN0IGFsbCwgb3IgYSBzZXQgb2Yga2V5cy5cbiAgICovIGdldCByYXdTZWxlY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLnNlbGVjdGVkS2V5cztcbiAgICB9XG4gICAgLyoqXG4gICAqIFJldHVybnMgd2hldGhlciBhIGtleSBpcyBzZWxlY3RlZC5cbiAgICovIGlzU2VsZWN0ZWQoa2V5KSB7XG4gICAgICAgIGlmICh0aGlzLnN0YXRlLnNlbGVjdGlvbk1vZGUgPT09ICdub25lJykgcmV0dXJuIGZhbHNlO1xuICAgICAgICBsZXQgbWFwcGVkS2V5ID0gdGhpcy5nZXRLZXkoa2V5KTtcbiAgICAgICAgaWYgKG1hcHBlZEtleSA9PSBudWxsKSByZXR1cm4gZmFsc2U7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLnNlbGVjdGVkS2V5cyA9PT0gJ2FsbCcgPyB0aGlzLmNhblNlbGVjdEl0ZW0obWFwcGVkS2V5KSA6IHRoaXMuc3RhdGUuc2VsZWN0ZWRLZXlzLmhhcyhtYXBwZWRLZXkpO1xuICAgIH1cbiAgICAvKipcbiAgICogV2hldGhlciB0aGUgc2VsZWN0aW9uIGlzIGVtcHR5LlxuICAgKi8gZ2V0IGlzRW1wdHkoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLnNlbGVjdGVkS2V5cyAhPT0gJ2FsbCcgJiYgdGhpcy5zdGF0ZS5zZWxlY3RlZEtleXMuc2l6ZSA9PT0gMDtcbiAgICB9XG4gICAgLyoqXG4gICAqIFdoZXRoZXIgYWxsIGl0ZW1zIGluIHRoZSBjb2xsZWN0aW9uIGFyZSBzZWxlY3RlZC5cbiAgICovIGdldCBpc1NlbGVjdEFsbCgpIHtcbiAgICAgICAgaWYgKHRoaXMuaXNFbXB0eSkgcmV0dXJuIGZhbHNlO1xuICAgICAgICBpZiAodGhpcy5zdGF0ZS5zZWxlY3RlZEtleXMgPT09ICdhbGwnKSByZXR1cm4gdHJ1ZTtcbiAgICAgICAgaWYgKHRoaXMuX2lzU2VsZWN0QWxsICE9IG51bGwpIHJldHVybiB0aGlzLl9pc1NlbGVjdEFsbDtcbiAgICAgICAgbGV0IGFsbEtleXMgPSB0aGlzLmdldFNlbGVjdEFsbEtleXMoKTtcbiAgICAgICAgbGV0IHNlbGVjdGVkS2V5cyA9IHRoaXMuc3RhdGUuc2VsZWN0ZWRLZXlzO1xuICAgICAgICB0aGlzLl9pc1NlbGVjdEFsbCA9IGFsbEtleXMuZXZlcnkoKGspPT5zZWxlY3RlZEtleXMuaGFzKGspKTtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2lzU2VsZWN0QWxsO1xuICAgIH1cbiAgICBnZXQgZmlyc3RTZWxlY3RlZEtleSgpIHtcbiAgICAgICAgbGV0IGZpcnN0ID0gbnVsbDtcbiAgICAgICAgZm9yIChsZXQga2V5IG9mIHRoaXMuc3RhdGUuc2VsZWN0ZWRLZXlzKXtcbiAgICAgICAgICAgIGxldCBpdGVtID0gdGhpcy5jb2xsZWN0aW9uLmdldEl0ZW0oa2V5KTtcbiAgICAgICAgICAgIGlmICghZmlyc3QgfHwgaXRlbSAmJiAoMCwgJGpraFVUJGNvbXBhcmVOb2RlT3JkZXIpKHRoaXMuY29sbGVjdGlvbiwgaXRlbSwgZmlyc3QpIDwgMCkgZmlyc3QgPSBpdGVtO1xuICAgICAgICB9XG4gICAgICAgIHZhciBfZmlyc3Rfa2V5O1xuICAgICAgICByZXR1cm4gKF9maXJzdF9rZXkgPSBmaXJzdCA9PT0gbnVsbCB8fCBmaXJzdCA9PT0gdm9pZCAwID8gdm9pZCAwIDogZmlyc3Qua2V5KSAhPT0gbnVsbCAmJiBfZmlyc3Rfa2V5ICE9PSB2b2lkIDAgPyBfZmlyc3Rfa2V5IDogbnVsbDtcbiAgICB9XG4gICAgZ2V0IGxhc3RTZWxlY3RlZEtleSgpIHtcbiAgICAgICAgbGV0IGxhc3QgPSBudWxsO1xuICAgICAgICBmb3IgKGxldCBrZXkgb2YgdGhpcy5zdGF0ZS5zZWxlY3RlZEtleXMpe1xuICAgICAgICAgICAgbGV0IGl0ZW0gPSB0aGlzLmNvbGxlY3Rpb24uZ2V0SXRlbShrZXkpO1xuICAgICAgICAgICAgaWYgKCFsYXN0IHx8IGl0ZW0gJiYgKDAsICRqa2hVVCRjb21wYXJlTm9kZU9yZGVyKSh0aGlzLmNvbGxlY3Rpb24sIGl0ZW0sIGxhc3QpID4gMCkgbGFzdCA9IGl0ZW07XG4gICAgICAgIH1cbiAgICAgICAgdmFyIF9sYXN0X2tleTtcbiAgICAgICAgcmV0dXJuIChfbGFzdF9rZXkgPSBsYXN0ID09PSBudWxsIHx8IGxhc3QgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGxhc3Qua2V5KSAhPT0gbnVsbCAmJiBfbGFzdF9rZXkgIT09IHZvaWQgMCA/IF9sYXN0X2tleSA6IG51bGw7XG4gICAgfVxuICAgIGdldCBkaXNhYmxlZEtleXMoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLmRpc2FibGVkS2V5cztcbiAgICB9XG4gICAgZ2V0IGRpc2FibGVkQmVoYXZpb3IoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLmRpc2FibGVkQmVoYXZpb3I7XG4gICAgfVxuICAgIC8qKlxuICAgKiBFeHRlbmRzIHRoZSBzZWxlY3Rpb24gdG8gdGhlIGdpdmVuIGtleS5cbiAgICovIGV4dGVuZFNlbGVjdGlvbih0b0tleSkge1xuICAgICAgICBpZiAodGhpcy5zZWxlY3Rpb25Nb2RlID09PSAnbm9uZScpIHJldHVybjtcbiAgICAgICAgaWYgKHRoaXMuc2VsZWN0aW9uTW9kZSA9PT0gJ3NpbmdsZScpIHtcbiAgICAgICAgICAgIHRoaXMucmVwbGFjZVNlbGVjdGlvbih0b0tleSk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgbGV0IG1hcHBlZFRvS2V5ID0gdGhpcy5nZXRLZXkodG9LZXkpO1xuICAgICAgICBpZiAobWFwcGVkVG9LZXkgPT0gbnVsbCkgcmV0dXJuO1xuICAgICAgICBsZXQgc2VsZWN0aW9uO1xuICAgICAgICAvLyBPbmx5IHNlbGVjdCB0aGUgb25lIGtleSBpZiBjb21pbmcgZnJvbSBhIHNlbGVjdCBhbGwuXG4gICAgICAgIGlmICh0aGlzLnN0YXRlLnNlbGVjdGVkS2V5cyA9PT0gJ2FsbCcpIHNlbGVjdGlvbiA9IG5ldyAoMCwgJGU0MGVhODI1YTgxYTM3MDkkZXhwb3J0JDUyYmFhYzIyNzI2YzcyYmYpKFtcbiAgICAgICAgICAgIG1hcHBlZFRvS2V5XG4gICAgICAgIF0sIG1hcHBlZFRvS2V5LCBtYXBwZWRUb0tleSk7XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgbGV0IHNlbGVjdGVkS2V5cyA9IHRoaXMuc3RhdGUuc2VsZWN0ZWRLZXlzO1xuICAgICAgICAgICAgdmFyIF9zZWxlY3RlZEtleXNfYW5jaG9yS2V5O1xuICAgICAgICAgICAgbGV0IGFuY2hvcktleSA9IChfc2VsZWN0ZWRLZXlzX2FuY2hvcktleSA9IHNlbGVjdGVkS2V5cy5hbmNob3JLZXkpICE9PSBudWxsICYmIF9zZWxlY3RlZEtleXNfYW5jaG9yS2V5ICE9PSB2b2lkIDAgPyBfc2VsZWN0ZWRLZXlzX2FuY2hvcktleSA6IG1hcHBlZFRvS2V5O1xuICAgICAgICAgICAgc2VsZWN0aW9uID0gbmV3ICgwLCAkZTQwZWE4MjVhODFhMzcwOSRleHBvcnQkNTJiYWFjMjI3MjZjNzJiZikoc2VsZWN0ZWRLZXlzLCBhbmNob3JLZXksIG1hcHBlZFRvS2V5KTtcbiAgICAgICAgICAgIHZhciBfc2VsZWN0ZWRLZXlzX2N1cnJlbnRLZXk7XG4gICAgICAgICAgICBmb3IgKGxldCBrZXkgb2YgdGhpcy5nZXRLZXlSYW5nZShhbmNob3JLZXksIChfc2VsZWN0ZWRLZXlzX2N1cnJlbnRLZXkgPSBzZWxlY3RlZEtleXMuY3VycmVudEtleSkgIT09IG51bGwgJiYgX3NlbGVjdGVkS2V5c19jdXJyZW50S2V5ICE9PSB2b2lkIDAgPyBfc2VsZWN0ZWRLZXlzX2N1cnJlbnRLZXkgOiBtYXBwZWRUb0tleSkpc2VsZWN0aW9uLmRlbGV0ZShrZXkpO1xuICAgICAgICAgICAgZm9yIChsZXQga2V5IG9mIHRoaXMuZ2V0S2V5UmFuZ2UobWFwcGVkVG9LZXksIGFuY2hvcktleSkpaWYgKHRoaXMuY2FuU2VsZWN0SXRlbShrZXkpKSBzZWxlY3Rpb24uYWRkKGtleSk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zdGF0ZS5zZXRTZWxlY3RlZEtleXMoc2VsZWN0aW9uKTtcbiAgICB9XG4gICAgZ2V0S2V5UmFuZ2UoZnJvbSwgdG8pIHtcbiAgICAgICAgbGV0IGZyb21JdGVtID0gdGhpcy5jb2xsZWN0aW9uLmdldEl0ZW0oZnJvbSk7XG4gICAgICAgIGxldCB0b0l0ZW0gPSB0aGlzLmNvbGxlY3Rpb24uZ2V0SXRlbSh0byk7XG4gICAgICAgIGlmIChmcm9tSXRlbSAmJiB0b0l0ZW0pIHtcbiAgICAgICAgICAgIGlmICgoMCwgJGpraFVUJGNvbXBhcmVOb2RlT3JkZXIpKHRoaXMuY29sbGVjdGlvbiwgZnJvbUl0ZW0sIHRvSXRlbSkgPD0gMCkgcmV0dXJuIHRoaXMuZ2V0S2V5UmFuZ2VJbnRlcm5hbChmcm9tLCB0byk7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5nZXRLZXlSYW5nZUludGVybmFsKHRvLCBmcm9tKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gW107XG4gICAgfVxuICAgIGdldEtleVJhbmdlSW50ZXJuYWwoZnJvbSwgdG8pIHtcbiAgICAgICAgdmFyIF90aGlzX2xheW91dERlbGVnYXRlO1xuICAgICAgICBpZiAoKF90aGlzX2xheW91dERlbGVnYXRlID0gdGhpcy5sYXlvdXREZWxlZ2F0ZSkgPT09IG51bGwgfHwgX3RoaXNfbGF5b3V0RGVsZWdhdGUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzX2xheW91dERlbGVnYXRlLmdldEtleVJhbmdlKSByZXR1cm4gdGhpcy5sYXlvdXREZWxlZ2F0ZS5nZXRLZXlSYW5nZShmcm9tLCB0byk7XG4gICAgICAgIGxldCBrZXlzID0gW107XG4gICAgICAgIGxldCBrZXkgPSBmcm9tO1xuICAgICAgICB3aGlsZShrZXkgIT0gbnVsbCl7XG4gICAgICAgICAgICBsZXQgaXRlbSA9IHRoaXMuY29sbGVjdGlvbi5nZXRJdGVtKGtleSk7XG4gICAgICAgICAgICBpZiAoaXRlbSAmJiAoaXRlbS50eXBlID09PSAnaXRlbScgfHwgaXRlbS50eXBlID09PSAnY2VsbCcgJiYgdGhpcy5hbGxvd3NDZWxsU2VsZWN0aW9uKSkga2V5cy5wdXNoKGtleSk7XG4gICAgICAgICAgICBpZiAoa2V5ID09PSB0bykgcmV0dXJuIGtleXM7XG4gICAgICAgICAgICBrZXkgPSB0aGlzLmNvbGxlY3Rpb24uZ2V0S2V5QWZ0ZXIoa2V5KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gW107XG4gICAgfVxuICAgIGdldEtleShrZXkpIHtcbiAgICAgICAgbGV0IGl0ZW0gPSB0aGlzLmNvbGxlY3Rpb24uZ2V0SXRlbShrZXkpO1xuICAgICAgICBpZiAoIWl0ZW0pIC8vIMKvXFxfKOODhClfL8KvXG4gICAgICAgIHJldHVybiBrZXk7XG4gICAgICAgIC8vIElmIGNlbGwgc2VsZWN0aW9uIGlzIGFsbG93ZWQsIGp1c3QgcmV0dXJuIHRoZSBrZXkuXG4gICAgICAgIGlmIChpdGVtLnR5cGUgPT09ICdjZWxsJyAmJiB0aGlzLmFsbG93c0NlbGxTZWxlY3Rpb24pIHJldHVybiBrZXk7XG4gICAgICAgIC8vIEZpbmQgYSBwYXJlbnQgaXRlbSB0byBzZWxlY3RcbiAgICAgICAgd2hpbGUoaXRlbSAmJiBpdGVtLnR5cGUgIT09ICdpdGVtJyAmJiBpdGVtLnBhcmVudEtleSAhPSBudWxsKWl0ZW0gPSB0aGlzLmNvbGxlY3Rpb24uZ2V0SXRlbShpdGVtLnBhcmVudEtleSk7XG4gICAgICAgIGlmICghaXRlbSB8fCBpdGVtLnR5cGUgIT09ICdpdGVtJykgcmV0dXJuIG51bGw7XG4gICAgICAgIHJldHVybiBpdGVtLmtleTtcbiAgICB9XG4gICAgLyoqXG4gICAqIFRvZ2dsZXMgd2hldGhlciB0aGUgZ2l2ZW4ga2V5IGlzIHNlbGVjdGVkLlxuICAgKi8gdG9nZ2xlU2VsZWN0aW9uKGtleSkge1xuICAgICAgICBpZiAodGhpcy5zZWxlY3Rpb25Nb2RlID09PSAnbm9uZScpIHJldHVybjtcbiAgICAgICAgaWYgKHRoaXMuc2VsZWN0aW9uTW9kZSA9PT0gJ3NpbmdsZScgJiYgIXRoaXMuaXNTZWxlY3RlZChrZXkpKSB7XG4gICAgICAgICAgICB0aGlzLnJlcGxhY2VTZWxlY3Rpb24oa2V5KTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBsZXQgbWFwcGVkS2V5ID0gdGhpcy5nZXRLZXkoa2V5KTtcbiAgICAgICAgaWYgKG1hcHBlZEtleSA9PSBudWxsKSByZXR1cm47XG4gICAgICAgIGxldCBrZXlzID0gbmV3ICgwLCAkZTQwZWE4MjVhODFhMzcwOSRleHBvcnQkNTJiYWFjMjI3MjZjNzJiZikodGhpcy5zdGF0ZS5zZWxlY3RlZEtleXMgPT09ICdhbGwnID8gdGhpcy5nZXRTZWxlY3RBbGxLZXlzKCkgOiB0aGlzLnN0YXRlLnNlbGVjdGVkS2V5cyk7XG4gICAgICAgIGlmIChrZXlzLmhhcyhtYXBwZWRLZXkpKSBrZXlzLmRlbGV0ZShtYXBwZWRLZXkpO1xuICAgICAgICBlbHNlIGlmICh0aGlzLmNhblNlbGVjdEl0ZW0obWFwcGVkS2V5KSkge1xuICAgICAgICAgICAga2V5cy5hZGQobWFwcGVkS2V5KTtcbiAgICAgICAgICAgIGtleXMuYW5jaG9yS2V5ID0gbWFwcGVkS2V5O1xuICAgICAgICAgICAga2V5cy5jdXJyZW50S2V5ID0gbWFwcGVkS2V5O1xuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLmRpc2FsbG93RW1wdHlTZWxlY3Rpb24gJiYga2V5cy5zaXplID09PSAwKSByZXR1cm47XG4gICAgICAgIHRoaXMuc3RhdGUuc2V0U2VsZWN0ZWRLZXlzKGtleXMpO1xuICAgIH1cbiAgICAvKipcbiAgICogUmVwbGFjZXMgdGhlIHNlbGVjdGlvbiB3aXRoIG9ubHkgdGhlIGdpdmVuIGtleS5cbiAgICovIHJlcGxhY2VTZWxlY3Rpb24oa2V5KSB7XG4gICAgICAgIGlmICh0aGlzLnNlbGVjdGlvbk1vZGUgPT09ICdub25lJykgcmV0dXJuO1xuICAgICAgICBsZXQgbWFwcGVkS2V5ID0gdGhpcy5nZXRLZXkoa2V5KTtcbiAgICAgICAgaWYgKG1hcHBlZEtleSA9PSBudWxsKSByZXR1cm47XG4gICAgICAgIGxldCBzZWxlY3Rpb24gPSB0aGlzLmNhblNlbGVjdEl0ZW0obWFwcGVkS2V5KSA/IG5ldyAoMCwgJGU0MGVhODI1YTgxYTM3MDkkZXhwb3J0JDUyYmFhYzIyNzI2YzcyYmYpKFtcbiAgICAgICAgICAgIG1hcHBlZEtleVxuICAgICAgICBdLCBtYXBwZWRLZXksIG1hcHBlZEtleSkgOiBuZXcgKDAsICRlNDBlYTgyNWE4MWEzNzA5JGV4cG9ydCQ1MmJhYWMyMjcyNmM3MmJmKSgpO1xuICAgICAgICB0aGlzLnN0YXRlLnNldFNlbGVjdGVkS2V5cyhzZWxlY3Rpb24pO1xuICAgIH1cbiAgICAvKipcbiAgICogUmVwbGFjZXMgdGhlIHNlbGVjdGlvbiB3aXRoIHRoZSBnaXZlbiBrZXlzLlxuICAgKi8gc2V0U2VsZWN0ZWRLZXlzKGtleXMpIHtcbiAgICAgICAgaWYgKHRoaXMuc2VsZWN0aW9uTW9kZSA9PT0gJ25vbmUnKSByZXR1cm47XG4gICAgICAgIGxldCBzZWxlY3Rpb24gPSBuZXcgKDAsICRlNDBlYTgyNWE4MWEzNzA5JGV4cG9ydCQ1MmJhYWMyMjcyNmM3MmJmKSgpO1xuICAgICAgICBmb3IgKGxldCBrZXkgb2Yga2V5cyl7XG4gICAgICAgICAgICBsZXQgbWFwcGVkS2V5ID0gdGhpcy5nZXRLZXkoa2V5KTtcbiAgICAgICAgICAgIGlmIChtYXBwZWRLZXkgIT0gbnVsbCkge1xuICAgICAgICAgICAgICAgIHNlbGVjdGlvbi5hZGQobWFwcGVkS2V5KTtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5zZWxlY3Rpb25Nb2RlID09PSAnc2luZ2xlJykgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zdGF0ZS5zZXRTZWxlY3RlZEtleXMoc2VsZWN0aW9uKTtcbiAgICB9XG4gICAgZ2V0U2VsZWN0QWxsS2V5cygpIHtcbiAgICAgICAgbGV0IGtleXMgPSBbXTtcbiAgICAgICAgbGV0IGFkZEtleXMgPSAoa2V5KT0+e1xuICAgICAgICAgICAgd2hpbGUoa2V5ICE9IG51bGwpe1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLmNhblNlbGVjdEl0ZW0oa2V5KSkge1xuICAgICAgICAgICAgICAgICAgICB2YXIgX2dldEZpcnN0SXRlbTtcbiAgICAgICAgICAgICAgICAgICAgbGV0IGl0ZW0gPSB0aGlzLmNvbGxlY3Rpb24uZ2V0SXRlbShrZXkpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoKGl0ZW0gPT09IG51bGwgfHwgaXRlbSA9PT0gdm9pZCAwID8gdm9pZCAwIDogaXRlbS50eXBlKSA9PT0gJ2l0ZW0nKSBrZXlzLnB1c2goa2V5KTtcbiAgICAgICAgICAgICAgICAgICAgdmFyIF9nZXRGaXJzdEl0ZW1fa2V5O1xuICAgICAgICAgICAgICAgICAgICAvLyBBZGQgY2hpbGQga2V5cy4gSWYgY2VsbCBzZWxlY3Rpb24gaXMgYWxsb3dlZCwgdGhlbiBpbmNsdWRlIGl0ZW0gY2hpbGRyZW4gdG9vLlxuICAgICAgICAgICAgICAgICAgICBpZiAoKGl0ZW0gPT09IG51bGwgfHwgaXRlbSA9PT0gdm9pZCAwID8gdm9pZCAwIDogaXRlbS5oYXNDaGlsZE5vZGVzKSAmJiAodGhpcy5hbGxvd3NDZWxsU2VsZWN0aW9uIHx8IGl0ZW0udHlwZSAhPT0gJ2l0ZW0nKSkgYWRkS2V5cygoX2dldEZpcnN0SXRlbV9rZXkgPSAoX2dldEZpcnN0SXRlbSA9ICgwLCAkamtoVVQkZ2V0Rmlyc3RJdGVtKSgoMCwgJGpraFVUJGdldENoaWxkTm9kZXMpKGl0ZW0sIHRoaXMuY29sbGVjdGlvbikpKSA9PT0gbnVsbCB8fCBfZ2V0Rmlyc3RJdGVtID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZ2V0Rmlyc3RJdGVtLmtleSkgIT09IG51bGwgJiYgX2dldEZpcnN0SXRlbV9rZXkgIT09IHZvaWQgMCA/IF9nZXRGaXJzdEl0ZW1fa2V5IDogbnVsbCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGtleSA9IHRoaXMuY29sbGVjdGlvbi5nZXRLZXlBZnRlcihrZXkpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICBhZGRLZXlzKHRoaXMuY29sbGVjdGlvbi5nZXRGaXJzdEtleSgpKTtcbiAgICAgICAgcmV0dXJuIGtleXM7XG4gICAgfVxuICAgIC8qKlxuICAgKiBTZWxlY3RzIGFsbCBpdGVtcyBpbiB0aGUgY29sbGVjdGlvbi5cbiAgICovIHNlbGVjdEFsbCgpIHtcbiAgICAgICAgaWYgKCF0aGlzLmlzU2VsZWN0QWxsICYmIHRoaXMuc2VsZWN0aW9uTW9kZSA9PT0gJ211bHRpcGxlJykgdGhpcy5zdGF0ZS5zZXRTZWxlY3RlZEtleXMoJ2FsbCcpO1xuICAgIH1cbiAgICAvKipcbiAgICogUmVtb3ZlcyBhbGwga2V5cyBmcm9tIHRoZSBzZWxlY3Rpb24uXG4gICAqLyBjbGVhclNlbGVjdGlvbigpIHtcbiAgICAgICAgaWYgKCF0aGlzLmRpc2FsbG93RW1wdHlTZWxlY3Rpb24gJiYgKHRoaXMuc3RhdGUuc2VsZWN0ZWRLZXlzID09PSAnYWxsJyB8fCB0aGlzLnN0YXRlLnNlbGVjdGVkS2V5cy5zaXplID4gMCkpIHRoaXMuc3RhdGUuc2V0U2VsZWN0ZWRLZXlzKG5ldyAoMCwgJGU0MGVhODI1YTgxYTM3MDkkZXhwb3J0JDUyYmFhYzIyNzI2YzcyYmYpKCkpO1xuICAgIH1cbiAgICAvKipcbiAgICogVG9nZ2xlcyBiZXR3ZWVuIHNlbGVjdCBhbGwgYW5kIGFuIGVtcHR5IHNlbGVjdGlvbi5cbiAgICovIHRvZ2dsZVNlbGVjdEFsbCgpIHtcbiAgICAgICAgaWYgKHRoaXMuaXNTZWxlY3RBbGwpIHRoaXMuY2xlYXJTZWxlY3Rpb24oKTtcbiAgICAgICAgZWxzZSB0aGlzLnNlbGVjdEFsbCgpO1xuICAgIH1cbiAgICBzZWxlY3Qoa2V5LCBlKSB7XG4gICAgICAgIGlmICh0aGlzLnNlbGVjdGlvbk1vZGUgPT09ICdub25lJykgcmV0dXJuO1xuICAgICAgICBpZiAodGhpcy5zZWxlY3Rpb25Nb2RlID09PSAnc2luZ2xlJykge1xuICAgICAgICAgICAgaWYgKHRoaXMuaXNTZWxlY3RlZChrZXkpICYmICF0aGlzLmRpc2FsbG93RW1wdHlTZWxlY3Rpb24pIHRoaXMudG9nZ2xlU2VsZWN0aW9uKGtleSk7XG4gICAgICAgICAgICBlbHNlIHRoaXMucmVwbGFjZVNlbGVjdGlvbihrZXkpO1xuICAgICAgICB9IGVsc2UgaWYgKHRoaXMuc2VsZWN0aW9uQmVoYXZpb3IgPT09ICd0b2dnbGUnIHx8IGUgJiYgKGUucG9pbnRlclR5cGUgPT09ICd0b3VjaCcgfHwgZS5wb2ludGVyVHlwZSA9PT0gJ3ZpcnR1YWwnKSkgLy8gaWYgdG91Y2ggb3IgdmlydHVhbCAoVk8pIHRoZW4gd2UganVzdCB3YW50IHRvIHRvZ2dsZSwgb3RoZXJ3aXNlIGl0J3MgaW1wb3NzaWJsZSB0byBtdWx0aSBzZWxlY3QgYmVjYXVzZSB0aGV5IGRvbid0IGhhdmUgbW9kaWZpZXIga2V5c1xuICAgICAgICB0aGlzLnRvZ2dsZVNlbGVjdGlvbihrZXkpO1xuICAgICAgICBlbHNlIHRoaXMucmVwbGFjZVNlbGVjdGlvbihrZXkpO1xuICAgIH1cbiAgICAvKipcbiAgICogUmV0dXJucyB3aGV0aGVyIHRoZSBjdXJyZW50IHNlbGVjdGlvbiBpcyBlcXVhbCB0byB0aGUgZ2l2ZW4gc2VsZWN0aW9uLlxuICAgKi8gaXNTZWxlY3Rpb25FcXVhbChzZWxlY3Rpb24pIHtcbiAgICAgICAgaWYgKHNlbGVjdGlvbiA9PT0gdGhpcy5zdGF0ZS5zZWxlY3RlZEtleXMpIHJldHVybiB0cnVlO1xuICAgICAgICAvLyBDaGVjayBpZiB0aGUgc2V0IG9mIGtleXMgbWF0Y2guXG4gICAgICAgIGxldCBzZWxlY3RlZEtleXMgPSB0aGlzLnNlbGVjdGVkS2V5cztcbiAgICAgICAgaWYgKHNlbGVjdGlvbi5zaXplICE9PSBzZWxlY3RlZEtleXMuc2l6ZSkgcmV0dXJuIGZhbHNlO1xuICAgICAgICBmb3IgKGxldCBrZXkgb2Ygc2VsZWN0aW9uKXtcbiAgICAgICAgICAgIGlmICghc2VsZWN0ZWRLZXlzLmhhcyhrZXkpKSByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgZm9yIChsZXQga2V5IG9mIHNlbGVjdGVkS2V5cyl7XG4gICAgICAgICAgICBpZiAoIXNlbGVjdGlvbi5oYXMoa2V5KSkgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICBjYW5TZWxlY3RJdGVtKGtleSkge1xuICAgICAgICB2YXIgX2l0ZW1fcHJvcHM7XG4gICAgICAgIGlmICh0aGlzLnN0YXRlLnNlbGVjdGlvbk1vZGUgPT09ICdub25lJyB8fCB0aGlzLnN0YXRlLmRpc2FibGVkS2V5cy5oYXMoa2V5KSkgcmV0dXJuIGZhbHNlO1xuICAgICAgICBsZXQgaXRlbSA9IHRoaXMuY29sbGVjdGlvbi5nZXRJdGVtKGtleSk7XG4gICAgICAgIGlmICghaXRlbSB8fCAoaXRlbSA9PT0gbnVsbCB8fCBpdGVtID09PSB2b2lkIDAgPyB2b2lkIDAgOiAoX2l0ZW1fcHJvcHMgPSBpdGVtLnByb3BzKSA9PT0gbnVsbCB8fCBfaXRlbV9wcm9wcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2l0ZW1fcHJvcHMuaXNEaXNhYmxlZCkgfHwgaXRlbS50eXBlID09PSAnY2VsbCcgJiYgIXRoaXMuYWxsb3dzQ2VsbFNlbGVjdGlvbikgcmV0dXJuIGZhbHNlO1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgaXNEaXNhYmxlZChrZXkpIHtcbiAgICAgICAgdmFyIF90aGlzX2NvbGxlY3Rpb25fZ2V0SXRlbV9wcm9wcywgX3RoaXNfY29sbGVjdGlvbl9nZXRJdGVtO1xuICAgICAgICByZXR1cm4gdGhpcy5zdGF0ZS5kaXNhYmxlZEJlaGF2aW9yID09PSAnYWxsJyAmJiAodGhpcy5zdGF0ZS5kaXNhYmxlZEtleXMuaGFzKGtleSkgfHwgISEoKF90aGlzX2NvbGxlY3Rpb25fZ2V0SXRlbSA9IHRoaXMuY29sbGVjdGlvbi5nZXRJdGVtKGtleSkpID09PSBudWxsIHx8IF90aGlzX2NvbGxlY3Rpb25fZ2V0SXRlbSA9PT0gdm9pZCAwID8gdm9pZCAwIDogKF90aGlzX2NvbGxlY3Rpb25fZ2V0SXRlbV9wcm9wcyA9IF90aGlzX2NvbGxlY3Rpb25fZ2V0SXRlbS5wcm9wcykgPT09IG51bGwgfHwgX3RoaXNfY29sbGVjdGlvbl9nZXRJdGVtX3Byb3BzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdGhpc19jb2xsZWN0aW9uX2dldEl0ZW1fcHJvcHMuaXNEaXNhYmxlZCkpO1xuICAgIH1cbiAgICBpc0xpbmsoa2V5KSB7XG4gICAgICAgIHZhciBfdGhpc19jb2xsZWN0aW9uX2dldEl0ZW1fcHJvcHMsIF90aGlzX2NvbGxlY3Rpb25fZ2V0SXRlbTtcbiAgICAgICAgcmV0dXJuICEhKChfdGhpc19jb2xsZWN0aW9uX2dldEl0ZW0gPSB0aGlzLmNvbGxlY3Rpb24uZ2V0SXRlbShrZXkpKSA9PT0gbnVsbCB8fCBfdGhpc19jb2xsZWN0aW9uX2dldEl0ZW0gPT09IHZvaWQgMCA/IHZvaWQgMCA6IChfdGhpc19jb2xsZWN0aW9uX2dldEl0ZW1fcHJvcHMgPSBfdGhpc19jb2xsZWN0aW9uX2dldEl0ZW0ucHJvcHMpID09PSBudWxsIHx8IF90aGlzX2NvbGxlY3Rpb25fZ2V0SXRlbV9wcm9wcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3RoaXNfY29sbGVjdGlvbl9nZXRJdGVtX3Byb3BzLmhyZWYpO1xuICAgIH1cbiAgICBnZXRJdGVtUHJvcHMoa2V5KSB7XG4gICAgICAgIHZhciBfdGhpc19jb2xsZWN0aW9uX2dldEl0ZW07XG4gICAgICAgIHJldHVybiAoX3RoaXNfY29sbGVjdGlvbl9nZXRJdGVtID0gdGhpcy5jb2xsZWN0aW9uLmdldEl0ZW0oa2V5KSkgPT09IG51bGwgfHwgX3RoaXNfY29sbGVjdGlvbl9nZXRJdGVtID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdGhpc19jb2xsZWN0aW9uX2dldEl0ZW0ucHJvcHM7XG4gICAgfVxuICAgIGNvbnN0cnVjdG9yKGNvbGxlY3Rpb24sIHN0YXRlLCBvcHRpb25zKXtcbiAgICAgICAgdGhpcy5jb2xsZWN0aW9uID0gY29sbGVjdGlvbjtcbiAgICAgICAgdGhpcy5zdGF0ZSA9IHN0YXRlO1xuICAgICAgICB2YXIgX29wdGlvbnNfYWxsb3dzQ2VsbFNlbGVjdGlvbjtcbiAgICAgICAgdGhpcy5hbGxvd3NDZWxsU2VsZWN0aW9uID0gKF9vcHRpb25zX2FsbG93c0NlbGxTZWxlY3Rpb24gPSBvcHRpb25zID09PSBudWxsIHx8IG9wdGlvbnMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9wdGlvbnMuYWxsb3dzQ2VsbFNlbGVjdGlvbikgIT09IG51bGwgJiYgX29wdGlvbnNfYWxsb3dzQ2VsbFNlbGVjdGlvbiAhPT0gdm9pZCAwID8gX29wdGlvbnNfYWxsb3dzQ2VsbFNlbGVjdGlvbiA6IGZhbHNlO1xuICAgICAgICB0aGlzLl9pc1NlbGVjdEFsbCA9IG51bGw7XG4gICAgICAgIHRoaXMubGF5b3V0RGVsZWdhdGUgPSAob3B0aW9ucyA9PT0gbnVsbCB8fCBvcHRpb25zID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvcHRpb25zLmxheW91dERlbGVnYXRlKSB8fCBudWxsO1xuICAgIH1cbn1cblxuXG5leHBvcnQgeyRkNDk2YzBhMjBiNmU1OGVjJGV4cG9ydCQ2YzhhNWFhYWQxM2M5ODUyIGFzIFNlbGVjdGlvbk1hbmFnZXJ9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9U2VsZWN0aW9uTWFuYWdlci5tb2R1bGUuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+selection@3.19.0_react@18.3.1/node_modules/@react-stately/selection/dist/SelectionManager.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+selection@3.19.0_react@18.3.1/node_modules/@react-stately/selection/dist/useMultipleSelectionState.mjs":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+selection@3.19.0_react@18.3.1/node_modules/@react-stately/selection/dist/useMultipleSelectionState.mjs ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMultipleSelectionState: () => (/* binding */ $7af3f5b51489e0b5$export$253fe78d46329472)\n/* harmony export */ });\n/* harmony import */ var _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Selection.mjs */ \"(ssr)/./node_modules/.pnpm/@react-stately+selection@3.19.0_react@18.3.1/node_modules/@react-stately/selection/dist/Selection.mjs\");\n/* harmony import */ var _react_stately_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-stately/utils */ \"(ssr)/./node_modules/.pnpm/@react-stately+utils@3.10.5_react@18.3.1/node_modules/@react-stately/utils/dist/useControlledState.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\nfunction $7af3f5b51489e0b5$var$equalSets(setA, setB) {\n    if (setA.size !== setB.size) return false;\n    for (let item of setA){\n        if (!setB.has(item)) return false;\n    }\n    return true;\n}\nfunction $7af3f5b51489e0b5$export$253fe78d46329472(props) {\n    let { selectionMode: selectionMode = 'none', disallowEmptySelection: disallowEmptySelection = false, allowDuplicateSelectionEvents: allowDuplicateSelectionEvents, selectionBehavior: selectionBehaviorProp = 'toggle', disabledBehavior: disabledBehavior = 'all' } = props;\n    // We want synchronous updates to `isFocused` and `focusedKey` after their setters are called.\n    // But we also need to trigger a react re-render. So, we have both a ref (sync) and state (async).\n    let isFocusedRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    let [, setFocused] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    let focusedKeyRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    let childFocusStrategyRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    let [, setFocusedKey] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    let selectedKeysProp = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>$7af3f5b51489e0b5$var$convertSelection(props.selectedKeys), [\n        props.selectedKeys\n    ]);\n    let defaultSelectedKeys = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>$7af3f5b51489e0b5$var$convertSelection(props.defaultSelectedKeys, new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)()), [\n        props.defaultSelectedKeys\n    ]);\n    let [selectedKeys, setSelectedKeys] = (0, _react_stately_utils__WEBPACK_IMPORTED_MODULE_2__.useControlledState)(selectedKeysProp, defaultSelectedKeys, props.onSelectionChange);\n    let disabledKeysProp = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>props.disabledKeys ? new Set(props.disabledKeys) : new Set(), [\n        props.disabledKeys\n    ]);\n    let [selectionBehavior, setSelectionBehavior] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(selectionBehaviorProp);\n    // If the selectionBehavior prop is set to replace, but the current state is toggle (e.g. due to long press\n    // to enter selection mode on touch), and the selection becomes empty, reset the selection behavior.\n    if (selectionBehaviorProp === 'replace' && selectionBehavior === 'toggle' && typeof selectedKeys === 'object' && selectedKeys.size === 0) setSelectionBehavior('replace');\n    // If the selectionBehavior prop changes, update the state as well.\n    let lastSelectionBehavior = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(selectionBehaviorProp);\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (selectionBehaviorProp !== lastSelectionBehavior.current) {\n            setSelectionBehavior(selectionBehaviorProp);\n            lastSelectionBehavior.current = selectionBehaviorProp;\n        }\n    }, [\n        selectionBehaviorProp\n    ]);\n    return {\n        selectionMode: selectionMode,\n        disallowEmptySelection: disallowEmptySelection,\n        selectionBehavior: selectionBehavior,\n        setSelectionBehavior: setSelectionBehavior,\n        get isFocused () {\n            return isFocusedRef.current;\n        },\n        setFocused (f) {\n            isFocusedRef.current = f;\n            setFocused(f);\n        },\n        get focusedKey () {\n            return focusedKeyRef.current;\n        },\n        get childFocusStrategy () {\n            return childFocusStrategyRef.current;\n        },\n        setFocusedKey (k, childFocusStrategy = 'first') {\n            focusedKeyRef.current = k;\n            childFocusStrategyRef.current = childFocusStrategy;\n            setFocusedKey(k);\n        },\n        selectedKeys: selectedKeys,\n        setSelectedKeys (keys) {\n            if (allowDuplicateSelectionEvents || !$7af3f5b51489e0b5$var$equalSets(keys, selectedKeys)) setSelectedKeys(keys);\n        },\n        disabledKeys: disabledKeysProp,\n        disabledBehavior: disabledBehavior\n    };\n}\nfunction $7af3f5b51489e0b5$var$convertSelection(selection, defaultValue) {\n    if (!selection) return defaultValue;\n    return selection === 'all' ? 'all' : new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)(selection);\n}\n\n\n\n//# sourceMappingURL=useMultipleSelectionState.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+selection@3.19.0_react@18.3.1/node_modules/@react-stately/selection/dist/useMultipleSelectionState.mjs\n");

/***/ })

};
;