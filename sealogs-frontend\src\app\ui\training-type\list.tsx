'use client'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>, Popover } from 'react-aria-components'
import { TrainingTypeType } from '../../../../types/training-type'
import { List } from '../../../components/skeletons'
import Filter from '@/components/filter'
import { useLazyQuery } from '@apollo/client'
import { CREW_TRAINING_TYPES } from '@/app/lib/graphQL/query'
import { isEmpty, trim } from 'lodash'
import { DataTable } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'

const TrainingScheduleList = () => {
    const [trainingTypes, setTrainingTypes] = useState([] as TrainingTypeType[])
    const [filter, setFilter] = useState({} as SearchFilter)
    const [isLoading, setIsLoading] = useState(true)
    const [keywordFilter, setKeywordFilter] = useState([] as any)
    const limit = 100
    const [page, setPage] = useState(0)
    const [queryTrainingTypes] = useLazyQuery(CREW_TRAINING_TYPES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readTrainingTypes.nodes
            const hasNextPage = response.readTrainingTypes.pageInfo.hasNextPage
            const totalCount = response.readTrainingTypes.pageInfo.totalCount
            if (data.length > 0) {
                setTrainingTypes([...trainingTypes, ...data])
                if (hasNextPage) {
                    const nextPage = page + 1
                    setPage(nextPage)
                    queryTrainingTypes({
                        variables: {
                            limit: limit,
                            offset: nextPage * limit,
                            filter: { ...filter, ...keywordFilter },
                        },
                    })
                }
            }
        },
        onError: (error: any) => {
            console.error('queryTrainingTypes error', error)
        },
    })
    useEffect(() => {
        if (isLoading) {
            setTrainingTypes([])
            setPage(0)
            loadTrainingTypes()
            setIsLoading(false)
        }
    }, [isLoading])
    const loadTrainingTypes = async (
        searchFilter: SearchFilter = { ...filter },
        searchkeywordFilter: any = keywordFilter,
    ) => {
        if (searchkeywordFilter.length > 0) {
            const promises = searchkeywordFilter.map(
                async (keywordFilter: any) => {
                    return await queryTrainingTypes({
                        variables: {
                            filter: { ...searchFilter, ...keywordFilter },
                        },
                    })
                },
            )
            let responses = await Promise.all(promises)
            // filter out empty results
            responses = responses.filter(
                (r: any) => r.data.readTrainingTypes.nodes.length > 0,
            )
            // flatten results
            responses = responses.flatMap(
                (r: any) => r.data.readTrainingTypes.nodes,
            )
            // filter out duplicates
            responses = responses.filter(
                (value: any, index: any, self: any) =>
                    self.findIndex((v: any) => v.id === value.id) === index,
            )
            setTrainingTypes(responses)
        } else {
            await queryTrainingTypes({
                variables: {
                    filter: searchFilter,
                },
            })
        }
    }

    const handleFilterOnChange = ({ type, data }: any) => {
        const searchFilter: SearchFilter = { ...filter }
        if (type === 'vessel') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.vessels = {
                    id: { in: data.map((item) => +item.value) },
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.vessels = { id: { contains: +data.value } }
            } else {
                delete searchFilter.vessels
            }
        }
        let keyFilter = keywordFilter
        if (type === 'keyword') {
            if (!isEmpty(trim(data.value))) {
                keyFilter = [
                    { title: { contains: data.value } },
                    { procedure: { contains: data.value } },
                ]
            } else {
                keyFilter = []
            }
        }
        setFilter(searchFilter)
        setKeywordFilter(keyFilter)
        setTrainingTypes([])
        setPage(0)
        loadTrainingTypes(searchFilter, keyFilter)
    }
    const columns = [
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader
                    column={column}
                    title="Nature of Training"
                />
            ),
            cell: ({ row }: { row: any }) => {
                const trainingType: any = row.original
                return (
                    <div className="px-6 py-4">
                        <Link
                            href={`/training-type/info?id=${trainingType.id}`}
                            className="   ">
                            {trainingType.title}
                        </Link>
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.title || ''
                const valueB = rowB?.original?.title || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'vehicles',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Vessels" />
            ),
            cell: ({ row }: { row: any }) => {
                const trainingType = row.original
                return (
                    <div className="px-6 py-4">
                        {trainingType?.vessels?.nodes.map(
                            (vessel: any, index: number) => {
                                if (index < 2) {
                                    return (
                                        <div
                                            key={vessel.id}
                                            className="    inline-block rounded px-3 py-1 text-sky-700 mr-2">
                                            {vessel.title}
                                        </div>
                                    )
                                }
                                if (index === 2) {
                                    return (
                                        <DialogTrigger key={vessel.id}>
                                            <Button className="inline-block    text-sky-700  rounded  mr-1 p-1 px-3 outline-none">
                                                +{' '}
                                                {trainingType.vessels.nodes
                                                    .length - 2}{' '}
                                                more
                                            </Button>
                                            <Popover>
                                                <div className="p-0 w-64 max-h-full   rounded text-sky-700">
                                                    {trainingType.vessels.nodes.map(
                                                        (
                                                            vessel: any,
                                                            index: number,
                                                        ) => (
                                                            <span key={index}>
                                                                {index > 1 && (
                                                                    <div className="flex cursor-pointer hover:bg-sky-200 items-center overflow-auto">
                                                                        <div className="ps-3 py-2">
                                                                            <div className="">
                                                                                {
                                                                                    vessel.title
                                                                                }
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                )}
                                                            </span>
                                                        ),
                                                    )}
                                                </div>
                                            </Popover>
                                        </DialogTrigger>
                                    )
                                }
                            },
                        )}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.vessels?.nodes?.[0]?.title || ''
                const valueB = rowB?.original?.vessels?.nodes?.[0]?.title || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'occursEvery',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader
                    column={column}
                    title="Occurs Every (days)"
                />
            ),
            cell: ({ row }: { row: any }) => {
                const trainingType = row.original
                return (
                    <div className="px-6 py-4 text-center">
                        {trainingType.occursEvery}
                    </div>
                )
            },
        },
        {
            accessorKey: 'mediumWarnWithin',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader
                    column={column}
                    title="Medium Warning Within (days)"
                />
            ),
            cell: ({ row }: { row: any }) => {
                const trainingType = row.original
                return (
                    <div className="px-6 py-4 text-center">
                        {trainingType.mediumWarnWithin}
                    </div>
                )
            },
        },
        {
            accessorKey: 'highWarnWithin',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader
                    column={column}
                    title="High Warning Within (days)"
                />
            ),
            cell: ({ row }: { row: any }) => {
                const trainingType = row.original
                return (
                    <div className="px-6 py-4 text-center">
                        {trainingType.highWarnWithin}
                    </div>
                )
            },
        },
    ]
    return (
        <div className="relative w-full rounded-lg border bg-card text-card-foreground shadow-sm p-6">
            <DataTable
                columns={columns}
                data={trainingTypes}
                pageSize={20}
                onChange={handleFilterOnChange}
            />
        </div>
    )
}

export default TrainingScheduleList
