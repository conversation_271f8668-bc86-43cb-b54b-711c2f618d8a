"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/new/page",{

/***/ "(app-pages-browser)/./src/app/ui/inventory/inventory-new.tsx":
/*!************************************************!*\
  !*** ./src/app/ui/inventory/inventory-new.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewInventory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var react_aria_components__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! react-aria-components */ \"(app-pages-browser)/./node_modules/.pnpm/react-aria-components@1.6.0_c73dd79e59f59972f050dd4780f2df6c/node_modules/react-aria-components/dist/Heading.mjs\");\n/* harmony import */ var _app_ui_editor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/ui/editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _components_file_upload__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/file-upload */ \"(app-pages-browser)/./src/components/file-upload.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Hash,Package,Paperclip,ShoppingBag,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Hash,Package,Paperclip,ShoppingBag,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Hash,Package,Paperclip,ShoppingBag,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Hash,Package,Paperclip,ShoppingBag,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Hash,Package,Paperclip,ShoppingBag,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Hash,Package,Paperclip,ShoppingBag,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Hash,Package,Paperclip,ShoppingBag,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_ui_sea_logs_button__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/sea-logs-button */ \"(app-pages-browser)/./src/components/ui/sea-logs-button.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewInventory(param) {\n    let { vesselID = 0 } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedCategories, setSelectedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [suppliers, setSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedSuppliers, setSelectedSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [location, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedVessel, setSelectedVessel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [openLocationDialog, setOpenLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openSupplierDialog, setOpenSupplierDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openCategoryDialog, setOpenCategoryDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    var description = \"\";\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const vesselList = activeVessels.map((item)=>({\n                ...item\n            }));\n        const appendedData = [\n            // { title: '-- Other --', id: 'newLocation' },\n            ...vesselList,\n            {\n                title: \"Other\",\n                id: \"0\"\n            }\n        ];\n        setVessels(appendedData);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getVesselList)(handleSetVessels);\n    const handelSetSuppliers = (data)=>{\n        const suppliersList = [\n            {\n                label: \" ---- Create Supplier ---- \",\n                value: \"newSupplier\"\n            },\n            ...data === null || data === void 0 ? void 0 : data.filter((supplier)=>supplier.name !== null).map((supplier)=>({\n                    label: supplier.name,\n                    value: supplier.id\n                }))\n        ];\n        setSuppliers(suppliersList);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getSupplier)(handelSetSuppliers);\n    const handleSetCategories = (data)=>{\n        const categoriesList = [\n            {\n                label: \" ---- Create Category ---- \",\n                value: \"newCategory\"\n            },\n            ...data === null || data === void 0 ? void 0 : data.filter((category)=>category.name !== null).map((category)=>({\n                    label: category.name,\n                    value: category.id\n                }))\n        ];\n        setCategories(categoriesList);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getInventoryCategory)(handleSetCategories);\n    const handleSetSelectedCategories = (selectedOption)=>{\n        if (selectedOption.find((option)=>option.value === \"newCategory\")) {\n            setOpenCategoryDialog(true);\n        }\n        setSelectedCategories(selectedOption.filter((option)=>option.value !== \"newCategory\"));\n    };\n    const handleEditorChange = (desc)=>{\n        description = desc;\n    };\n    const handleCreate = async ()=>{\n        const variables = {\n            input: {\n                item: document.getElementById(\"inventory-name\").value ? document.getElementById(\"inventory-name\").value : null,\n                description: document.getElementById(\"inventory-short-description\").value ? document.getElementById(\"inventory-short-description\").value : null,\n                content: description,\n                quantity: document.getElementById(\"inventory-qty\").value ? parseInt(document.getElementById(\"inventory-qty\").value) : null,\n                productCode: document.getElementById(\"inventory-code\").value ? document.getElementById(\"inventory-code\").value : null,\n                costingDetails: document.getElementById(\"inventory-cost\").value ? document.getElementById(\"inventory-cost\").value : null,\n                documents: documents.map((doc)=>doc.id).join(\",\"),\n                categories: (selectedCategories === null || selectedCategories === void 0 ? void 0 : selectedCategories.map((category)=>category.value).length) ? selectedCategories.map((category)=>category.value).join(\",\") : null,\n                suppliers: (selectedSuppliers === null || selectedSuppliers === void 0 ? void 0 : selectedSuppliers.map((supplier)=>supplier.value).length) ? selectedSuppliers.map((supplier)=>supplier.value).join(\",\") : null,\n                location: document.getElementById(\"inventory-location\").value ? document.getElementById(\"inventory-location\").value : null,\n                vesselID: vesselID > 0 ? vesselID : selectedLocation === null || selectedLocation === void 0 ? void 0 : selectedLocation.value\n            }\n        };\n        await mutationCreateInventory({\n            variables\n        });\n    };\n    const [mutationCreateInventory, { loading: mutationcreateInventoryLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_INVENTORY, {\n        onCompleted: (response)=>{\n            const data = response.createInventory;\n            if (data.id > 0) {\n                searchParams.get(\"redirect_to\") ? router.push((searchParams === null || searchParams === void 0 ? void 0 : searchParams.get(\"redirect_to\")) + \"\") : router.back();\n            } else {\n                console.error(\"mutationcreateInventory error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationcreateInventory error\", error);\n        }\n    });\n    const handleCreateCategory = async ()=>{\n        const categoryName = document.getElementById(\"inventory-new-category\").value;\n        return await mutationcreateInventoryCategory({\n            variables: {\n                input: {\n                    name: categoryName\n                }\n            }\n        });\n    };\n    const [mutationcreateInventoryCategory, { loading: mutationcreateInventoryCategoryLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_INVENTORY_CATEGORY, {\n        onCompleted: (response)=>{\n            const data = response.createInventoryCategory;\n            if (data.id > 0) {\n                const categoriesList = [\n                    ...categories,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setCategories(categoriesList);\n                const selectedCategoriesList = [\n                    ...selectedCategories,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSelectedCategories(selectedCategoriesList);\n                setOpenCategoryDialog(false);\n            } else {\n                console.error(\"mutationcreateInventoryCategory error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationcreateInventoryCategory error\", error);\n        }\n    });\n    const handleSelectedVesselChange = (selectedOption)=>{\n        if (selectedOption.value === \"newLocation\") {\n            setOpenLocationDialog(true);\n        }\n        setSelectedLocation(selectedOption);\n    };\n    const handleCreateLocation = (Location)=>{\n        var newLocation = {\n            label: \"\",\n            value: \"\"\n        };\n        if (typeof Location === \"string\") {\n            newLocation = {\n                label: Location,\n                value: Location\n            };\n        }\n        if (typeof Location === \"object\") {\n            newLocation = {\n                label: document.getElementById(\"inventory-new-location\").value,\n                value: document.getElementById(\"inventory-new-location-id\").value ? document.getElementById(\"inventory-new-location-id\").value : document.getElementById(\"inventory-new-location\").value\n            };\n        }\n        const vesselList = vessels.map((item)=>({\n                ...item\n            }));\n        const appendedData = [\n            ...vesselList,\n            {\n                Title: newLocation.label,\n                ID: newLocation.value\n            }\n        ];\n        setVessels(appendedData);\n        setSelectedLocation(newLocation);\n        setOpenLocationDialog(false);\n    };\n    const handleSelectedSuppliers = (selectedOption)=>{\n        if (selectedOption.find((option)=>option.value === \"newSupplier\")) {\n            setOpenSupplierDialog(true);\n        }\n        setSelectedSuppliers(selectedOption.filter((option)=>option.value !== \"newSupplier\"));\n    };\n    const handleCreateSupplier = async ()=>{\n        const name = document.getElementById(\"supplier-name\").value;\n        const website = document.getElementById(\"supplier-website\").value;\n        const phone = document.getElementById(\"supplier-phone\").value;\n        const email = document.getElementById(\"supplier-email\").value;\n        const address = document.getElementById(\"supplier-address\").value;\n        const variables = {\n            input: {\n                name: name,\n                address: address,\n                website: website,\n                email: email,\n                phone: phone\n            }\n        };\n        if (name !== \"\") {\n            await mutationCreateSupplier({\n                variables\n            });\n        }\n        setOpenSupplierDialog(false);\n    };\n    const [mutationCreateSupplier, { loading: mutationcreateSupplierLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_SUPPLIER, {\n        onCompleted: (response)=>{\n            const data = response.createSupplier;\n            if (data.id > 0) {\n                const suppliersList = [\n                    ...suppliers,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSuppliers(suppliersList);\n                const selectedSuppliersList = [\n                    ...selectedSuppliers,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSelectedSuppliers(selectedSuppliersList);\n            } else {\n                console.error(\"mutationcreateSupplier error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationcreateSupplier error\", error);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.Card, {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.CardHeader, {\n                        className: \"border-b pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.CardTitle, {\n                            className: \"text-2xl font-medium flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-6 w-6 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 25\n                                }, this),\n                                \"New Inventory\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                htmlFor: \"inventory-name\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Inventory Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                id: \"inventory-name\",\n                                                type: \"text\",\n                                                placeholder: \"Inventory name\",\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                htmlFor: \"inventory-vessel\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Vessel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 29\n                                            }, this),\n                                            vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                                                id: \"inventory-vessel\",\n                                                options: vessels === null || vessels === void 0 ? void 0 : vessels.map((vessel)=>({\n                                                        label: vessel.title,\n                                                        value: vessel.id\n                                                    })),\n                                                defaultValues: vesselID > 0 && vessels.filter((vessel)=>vessel.id === vesselID).map((vessel)=>({\n                                                        label: vessel.title,\n                                                        value: vessel.id\n                                                    })),\n                                                placeholder: \"Select Vessel \".concat(vesselID),\n                                                onChange: handleSelectedVesselChange\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 33\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_6__.InputSkeleton, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                htmlFor: \"inventory-location\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Location\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                id: \"inventory-location\",\n                                                type: \"text\",\n                                                placeholder: \"Location\",\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                htmlFor: \"inventory-qty\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Quantity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                id: \"inventory-qty\",\n                                                type: \"number\",\n                                                placeholder: \"Quantity\",\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        htmlFor: \"inventory-short-description\",\n                                        className: \"text-sm font-medium flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 29\n                                            }, this),\n                                            \"Short Description\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                        id: \"inventory-short-description\",\n                                        rows: 12,\n                                        className: \"w-full resize-none\",\n                                        placeholder: \"Short description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 29\n                                            }, this),\n                                            \"Inventory Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm leading-relaxed\",\n                                        children: \"In this section categorise the item and add the suppliers where you normally purchase this item and the expected cost. This will help replacing the item in the future.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2 space-y-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                htmlFor: \"inventory-code\",\n                                                className: \"text-sm font-medium flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    \"Product code\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                id: \"inventory-code\",\n                                                type: \"text\",\n                                                placeholder: \"Product code\",\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                htmlFor: \"inventory-categories\",\n                                                className: \"text-sm font-medium flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    \"Categories\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 29\n                                            }, this),\n                                            categories ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                                                id: \"inventory-categories\",\n                                                multi: true,\n                                                options: categories,\n                                                value: selectedCategories,\n                                                onChange: handleSetSelectedCategories\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 33\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_6__.InputSkeleton, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                htmlFor: \"inventory-suppliers\",\n                                                className: \"text-sm font-medium flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    \"Supplier\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 29\n                                            }, this),\n                                            suppliers ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                                                id: \"inventory-suppliers\",\n                                                multi: true,\n                                                value: selectedSuppliers,\n                                                onChange: handleSelectedSuppliers,\n                                                options: suppliers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 33\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_6__.InputSkeleton, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                htmlFor: \"inventory-cost\",\n                                                className: \"text-sm font-medium flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    \"Cost\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                id: \"inventory-cost\",\n                                                type: \"text\",\n                                                placeholder: \"Costing Details\",\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 594,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 29\n                                            }, this),\n                                            \"Attachment\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm leading-relaxed\",\n                                        children: \"Upload things like photos of the item, plus warranty and guarantee documents or operating manuals. Add links to any online manuals or product descriptions.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2 space-y-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_file_upload__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            setDocuments: setDocuments,\n                                            text: \"\",\n                                            subText: \"Drag files here or upload\",\n                                            bgClass: \"bg-muted/30 border-2 border-dashed border-muted-foreground/20 rounded-lg p-6\",\n                                            documents: documents\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                htmlFor: \"inventory-links\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Links\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                id: \"inventory-links\",\n                                                type: \"text\",\n                                                placeholder: \"Links to manuals or product descriptions\",\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 610,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 597,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Hash_Package_Paperclip_ShoppingBag_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 29\n                                            }, this),\n                                            \"Description\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm leading-relaxed\",\n                                        children: \"Enter details that might help with the maintenance or operation of this item.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 641,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    id: \"inventory-Content\",\n                                    handleEditorChange: handleEditorChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 400,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_13__.FooterWrapper, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sea_logs_button__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        action: ()=>router.back(),\n                        type: \"text\",\n                        text: \"Cancel\",\n                        className: \"hover:bg-muted\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 661,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sea_logs_button__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        color: \"sky\",\n                        action: handleCreate,\n                        icon: \"check\",\n                        type: \"primary\",\n                        text: \"Create Inventory\",\n                        className: \"bg-primary text-primary-foreground hover:bg-primary/90\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 667,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 660,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.AlertDialogNew, {\n                openDialog: openLocationDialog,\n                setOpenDialog: setOpenLocationDialog,\n                handleCreate: ()=>handleCreateLocation({}),\n                actionText: \"Create Location\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_aria_components__WEBPACK_IMPORTED_MODULE_24__.Heading, {\n                        slot: \"title\",\n                        className: \"text-2xl  leading-6 my-2 \",\n                        children: \"Create New Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 683,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                            id: \"inventory-new-location\",\n                            type: \"text\",\n                            placeholder: \"Location\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                            lineNumber: 687,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 686,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                            id: \"inventory-new-location-id\",\n                            type: \"text\",\n                            placeholder: \"Location ID\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 693,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 678,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.AlertDialogNew, {\n                openDialog: openSupplierDialog,\n                setOpenDialog: setOpenSupplierDialog,\n                handleCreate: handleCreateSupplier,\n                actionText: \"Create Supplier\",\n                className: \"lg:max-w-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_aria_components__WEBPACK_IMPORTED_MODULE_24__.Heading, {\n                        slot: \"title\",\n                        className: \"text-2xl  leading-6 my-2 \",\n                        children: \"Create New Supplier\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 708,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                    id: \"supplier-name\",\n                                    type: \"text\",\n                                    placeholder: \"Supplier name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                    id: \"supplier-website\",\n                                    type: \"text\",\n                                    placeholder: \"Website\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 719,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                    id: \"supplier-phone\",\n                                    type: \"text\",\n                                    placeholder: \"Phone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 726,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                    id: \"supplier-email\",\n                                    type: \"email\",\n                                    placeholder: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 734,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 733,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                    id: \"supplier-address\",\n                                    rows: 4,\n                                    className: \" p-2\",\n                                    placeholder: \"Supplier address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 740,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 702,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_15__.AlertDialogNew, {\n                openDialog: openCategoryDialog,\n                setOpenDialog: setOpenCategoryDialog,\n                handleCreate: handleCreateCategory,\n                actionText: \"Create Category\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_aria_components__WEBPACK_IMPORTED_MODULE_24__.Heading, {\n                        slot: \"title\",\n                        className: \"text-2xl  leading-6 my-2 \",\n                        children: \"Create New Category\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 756,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                            id: \"inventory-new-category\",\n                            type: \"text\",\n                            placeholder: \"Category\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                            lineNumber: 760,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 759,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 751,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NewInventory, \"oNVcfeakna0lpaiWO9Usb234muo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_16__.useMutation\n    ];\n});\n_c = NewInventory;\nvar _c;\n$RefreshReg$(_c, \"NewInventory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/inventory/inventory-new.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/sea-logs-button.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/sea-logs-button.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst SeaLogsButton = (param)=>{\n    let { className, icon, action, color = \"\", counter = 0, counterColor, isDisabled, link, text = \"\", type = \"text\" } = param;\n    const icons = [\n        {\n            saveme: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                id: \"a\",\n                \"data-name\": \"Layer 1\",\n                className: \"-ml-1.5 mr-1.5 h-7 w-7\",\n                viewBox: \"0 0 507.69 507.69\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        cx: \"253.85\",\n                        cy: \"253.85\",\n                        r: \"253.85\",\n                        fill: \"#f4fafe\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M107.67,370.53c1.26-34.58,2.51-69.15,3.77-103.73l89.77,26.81-5.11,17.1c-12.85-3.05-26.04,4.35-29.87,17.16-3.4,11.4,2.58,22.95,10.42,28.3,2.99,2.04,5.66,2.78,6.47,2.98,10.26,2.63,26.87-2.11,31.28-16.89,3.83-12.81-3.15-26.23-15.56-30.73l5.11-17.1,68.11,20.34-12.67,48.77c-7.86,3.69-21.44,14.9-26.18,16.99-7.96,3.52-10.18,5.8-16.06,6.23-8.38.62-15.2-3.17-17.36-4.37-4.86-2.7-8.29-6.18-11.29-9.24-4.55-4.63-11.96-13.84-13.53-16.24-4.11-6.26-.03-.89-17.38,9.84-6.01,3.71-6.4,5.23-13.66,8.88-2.37,1.19-8.45,4.17-16.4,3.74-10.45-.57-17.52-6.66-19.85-8.86Z\",\n                                        fill: \"#2a99ea\",\n                                        stroke: \"#000\",\n                                        strokeMiterlimit: \"10\",\n                                        strokeWidth: \"3.19px\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M345.56,336.74l89.77,26.81-15.4,15.35c8-8.47,2.02-1.17-8.74,8.26-2.13,1.03-4.63,4.17-8.08,6.82-1.88,1.45-8.92,5.81-12.04,7.27-5.64,2.63-9.12,4.2-12.37,4.49-3.75.33-11.59.92-19.99-2.81-3.93-1.74-8.02-3.86-10.68-5.9-4.35-3.34-19.25-11.94-14.88-15.62\",\n                                        fill: \"#2a99ea\",\n                                        stroke: \"#000\",\n                                        strokeMiterlimit: \"10\",\n                                        strokeWidth: \"3.19px\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M344.41,335.99c.77.68-10.6,43.16-11.93,47.18-1.18,3.54-24.83,12.68-30.83,13.34-7.34.81-6.9,1.61-16.58-2.22-12.3-4.87-23.92-27.73-26.03-31.03,6.34-20.56,9.5-27.24,15.84-47.8l68.02,20.32\",\n                                        fill: \"#2a99ea\",\n                                        stroke: \"#000\",\n                                        strokeMiterlimit: \"10\",\n                                        strokeWidth: \"3.19px\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M352.82,286.76l45.33,13.54-6.67,22.32-85.03-25.4c-.76-.23-1.55.2-1.78.96s.2,1.55.96,1.78l85.03,25.4-6.72,22.5-145.42-43.44,21.87-73.21,145.42,43.44-6.85,22.92-45.33-13.54c-.76-.23-1.55.2-1.78.96s.2,1.55.96,1.78Z\",\n                                        fill: \"none\",\n                                        stroke: \"#000\",\n                                        strokeMiterlimit: \"10\",\n                                        strokeWidth: \"3.19px\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M223.74,199.97c3.18-10.64,14.43-16.72,25.08-13.54l191.23,57.12c10.65,3.18,16.72,14.43,13.54,25.07l-27.43,91.84-39.48-11.79,22.28-74.58c.23-.76-.2-1.55-.96-1.78l-148.16-44.26c-.76-.23-1.55.2-1.78.96l-22.28,74.58-39.48-11.79,27.43-91.84Z\",\n                                        fill: \"#f2f4f7\",\n                                        stroke: \"#000\",\n                                        strokeMiterlimit: \"10\",\n                                        strokeWidth: \"3.19px\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M47.46,366.69c-.79-.09-1.49.48-1.58,1.26s.48,1.49,1.26,1.58c15.94,1.75,25.42-12.12,37-23.2,8.9,13.33,29.13,30.46,45.06,32.21s32.15-14.03,43.59-24.29c8.94,12.5,20.99,31.38,36.28,33.06,15.94,1.75,37.59-13.81,49.17-24.89,8.9,13.33,16.96,32.15,32.89,33.9,15.26,1.68,30.41-3.21,41.85-13.44,8.94,12.47,22.67,20.53,37.93,22.2,15.94,1.75,37.65-14.82,49.23-25.9,8.9,13.33,16.9,33.16,32.83,34.91.79.09,1.49-.48,1.58-1.26s.04-1.44,4.85-9.12M106.08,366.87,92.66,260c-.44-.05-.87.11-1.18.43\",\n                                fill: \"none\",\n                                stroke: \"#000\",\n                                strokeMiterlimit: \"10\",\n                                strokeWidth: \"4.26px\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 56,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            trash: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                strokeWidth: \"1.5\",\n                stroke: \"currentColor\",\n                className: \"-ml-0.5 mr-1.5 h-5 w-5 border accent\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M6 18 18 6M6 6l12 12\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 21\n                }, undefined)\n            }, text, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 114,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            back_arrow: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"-ml-0.5 mr-1.5 h-5 w-5\",\n                viewBox: \"0 0 16.04 16.14\",\n                fill: \"currentColor\",\n                \"aria-hidden\": \"true\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M15,0c.2,0,.*******.*******.3,1.1l-2.4,6.7,2.4,6.7c.1.4,0,.8-.3,1.1-.3.3-.7.3-1.1.1L.6,9C.3,8.8,0,8.5,0,8.1s.2-.7.6-.9L14.6.2C14.7,0,14.9,0,15,0ZM13.2,13l-1.7-4.6c-.1-.2-.1-.5,0-.7l1.7-4.7L3.2,8l10,5Z\",\n                    clipRule: \"evenodd\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 21\n                }, undefined)\n            }, text, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 131,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            check: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"-ml-0.5 mr-1.5 h-5 w-5 border rounded-full   group-hover:\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                \"aria-hidden\": \"true\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z\",\n                    clipRule: \"evenodd\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 21\n                }, undefined)\n            }, text, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 147,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            pencil: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"-ml-0.5 mr-1.5 h-5 w-5 group-hover:border-white\",\n                viewBox: \"0 0 36 36\",\n                fill: \"currentColor\",\n                \"aria-hidden\": \"true\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M33.87,8.32,28,2.42a2.07,2.07,0,0,0-2.92,0L4.27,23.2l-1.9,8.2a2.06,2.06,0,0,0,2,2.5,2.14,2.14,0,0,0,.43,0L13.09,32,33.87,11.24A2.07,2.07,0,0,0,33.87,8.32ZM12.09,30.2,4.32,31.83l1.77-7.62L21.66,8.7l6,6ZM29,13.25l-6-6,3.48-3.46,5.9,6Z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 21\n                }, undefined)\n            }, text, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 163,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            record: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"-ml-0.5 mr-1.5 h-5 w-5 group-hover:border-white\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                \"aria-hidden\": \"true\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M3,4.5 L3,6.5 C3,6.77614237 3.22385763,7 3.5,7 L20.5,7 C20.7761424,7 21,6.77614237 21,6.5 L21,4.5 C21,4.22385763 20.7761424,4 20.5,4 L3.5,4 C3.22385763,4 3,4.22385763 3,4.5 Z M21,7.91464715 L21,18.5 C21,19.8807119 19.8807119,21 18.5,21 L5.5,21 C4.11928813,21 3,19.8807119 3,18.5 L3,7.91464715 C2.41740381,7.70872894 2,7.15310941 2,6.5 L2,4.5 C2,3.67157288 2.67157288,3 3.5,3 L20.5,3 C21.3284271,3 22,3.67157288 22,4.5 L22,6.5 C22,7.15310941 21.5825962,7.70872894 21,7.91464715 L21,7.91464715 Z M20,8 L4,8 L4,18.5 C4,19.3284271 4.67157288,20 5.5,20 L18.5,20 C19.3284271,20 20,19.3284271 20,18.5 L20,8 Z M8,11.5 C8,10.6715729 8.67157288,10 9.5,10 L14.5,10 C15.3284271,10 16,10.6715729 16,11.5 C16,12.3284271 15.3284271,13 14.5,13 L9.5,13 C8.67157288,13 8,12.3284271 8,11.5 Z M9,11.5 C9,11.7761424 9.22385763,12 9.5,12 L14.5,12 C14.7761424,12 15,11.7761424 15,11.5 C15,11.2238576 14.7761424,11 14.5,11 L9.5,11 C9.22385763,11 9,11.2238576 9,11.5 Z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 21\n                }, undefined)\n            }, text, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 175,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            category: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                strokeWidth: \"1.5\",\n                stroke: \"currentColor\",\n                className: \"-ml-0.5 mr-1.5 h-5 w-5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        opacity: \"0.34\",\n                        d: \"M5 10H7C9 10 10 9 10 7V5C10 3 9 2 7 2H5C3 2 2 3 2 5V7C2 9 3 10 5 10Z\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"1.5\",\n                        strokeMiterlimit: \"10\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M17 10H19C21 10 22 9 22 7V5C22 3 21 2 19 2H17C15 2 14 3 14 5V7C14 9 15 10 17 10Z\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"1.5\",\n                        strokeMiterlimit: \"10\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        opacity: \"0.34\",\n                        d: \"M17 22H19C21 22 22 21 22 19V17C22 15 21 14 19 14H17C15 14 14 15 14 17V19C14 21 15 22 17 22Z\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"1.5\",\n                        strokeMiterlimit: \"10\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M5 22H7C9 22 10 21 10 19V17C10 15 9 14 7 14H5C3 14 2 15 2 17V19C2 21 3 22 5 22Z\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"1.5\",\n                        strokeMiterlimit: \"10\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, text, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 187,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            qualification: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                viewBox: \"0 0 360.1359 469.8057\",\n                className: \"-ml-0.5 mr-1.5 h-5 w-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M3.9895.021C1.6535.4759-.024,2.5347.0022,4.9146v459.8162c-.0799,2.7215,2.0619,4.9927,4.7837,5.0727.0366.0014.0733.0021.1099.0021h350.3444c2.7229-.0191,4.9148-2.2422,4.8956-4.9651-.0003-.0361-.0009-.0729-.002-.1097V4.9146c-.02-2.6942-2.1993-4.8734-4.8936-4.8936H4.8958c-.3014-.028-.6048-.028-.9062,0ZM9.7894,9.8082h340.5601v450.0318H9.7894s0-450.0318,0-450.0318ZM70.6876,76.5062c-2.7528,0-4.9844,2.2316-4.9844,4.9842s2.2316,4.9846,4.9844,4.9846h77.2073c2.7528,0,4.9844-2.2316,4.9844-4.9846s-2.2316-4.9842-4.9844-4.9842h-77.2073ZM69.6001,142.2994c-2.687.4407-4.5079,2.9767-4.0671,5.6635.4088,2.4918,2.6339,4.2716,5.1545,4.1237h218.9435c2.7027.332,5.1627-1.5898,5.4946-4.2925.3319-2.7027-1.5899-5.1626-4.2926-5.4947-.3992-.0489-.8029-.0489-1.2021,0H70.6876c-.3614-.04-.7261-.04-1.0875,0ZM70.6876,207.9072c-2.7528,0-4.9844,2.2316-4.9844,4.9842s2.2316,4.9846,4.9844,4.9846h218.9435c2.7528,0,4.9844-2.2316,4.9844-4.9846s-2.2316-4.9842-4.9844-4.9842c0,0-218.9435,0-218.9435,0ZM105.6675,283.6646c-19.5866,0-35.343,16.0405-35.343,36.068,0,5.4908,1.1395,10.7628,3.2624,15.4054.203.2244.3849.4673.5434.725.6778,1.37,1.3449,2.7257,2.1749,3.9874.029.0581-.029.1391,0,.1798.808,1.2181,1.7791,2.3395,2.7187,3.444.5231.6106,1.0692,1.2393,1.6312,1.8124.4913.5044,1.1121.9749,1.6312,1.45,3.2985,3.0189,6.9882,5.3173,11.2372,6.8873,1.4163.5221,3.0326.9328,4.5311,1.2684,1.3312.3016,2.6033.583,3.9874.725h.9059c.9008.0697,1.7998.1798,2.7187.1798,1.7999,0,3.5274-.0988,5.2561-.3597,1.7961-.2669,3.5609-.5508,5.2561-1.0875,1.2439-.3887,2.4435-.9278,3.6249-1.45.9335-.4117,1.8294-.7749,2.7187-1.2684.4112-.2262.8674-.4814,1.2687-.725.0719-.0581.1096-.1335.181-.1798,2.4918-1.5501,4.8479-3.5042,6.8873-5.6189,1.0491-1.0875,1.9896-2.2284,2.8999-3.4433.9103-1.2156,1.7789-2.4783,2.5374-3.8065s1.3998-2.742,1.9937-4.1683c.1007-.2513.222-.4938.3625-.725.0588-.0616.1191-.1214.181-.1798.029-.0754.1525-.1101.181-.1798,1.4737-3.9654,2.1749-8.362,2.1749-12.8687,0-20.0275-15.9373-36.0673-35.5239-36.0673l.0017-.0046ZM143.3664,345.2878c-.7301,1.1059-1.5364,2.0457-2.3562,3.0815-.1386.1742-.2216.3713-.3625.5451-.668.822-1.4538,1.5852-2.1749,2.3558-1.7218,1.834-3.6202,3.5484-5.6186,5.0748-1.007.7696-2.0114,1.491-3.0812,2.1749-3.1827,2.0348-6.6808,3.7056-10.3307,4.8936l31.7181,50.9297,5.0748-14.3183c.767-2.1884,2.9549-3.5463,5.2561-3.2624l14.8621,1.6312-32.9865-53.1043-.0006-.0018ZM69.5984,347.4627l-32.08,50.9297,15.0436-1.6312c2.3012-.2839,4.4891,1.074,5.2561,3.2624l4.8936,14.1371,31.355-50.0235c-9.876-2.6613-18.4539-8.6604-24.4683-16.6745Z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 21\n                }, undefined)\n            }, text, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 233,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            link: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"-ml-0.5 mr-1.5 h-5 w-5 text-\".concat(color, \"-400\"),\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                \"aria-hidden\": \"true\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M12.232 4.232a2.5 2.5 0 013.536 3.536l-1.225 1.224a.75.75 0 001.061 1.06l1.224-1.224a4 4 0 00-5.656-5.656l-3 3a4 4 0 00.225 5.865.75.75 0 00.977-1.138 2.5 2.5 0 01-.142-3.667l3-3z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M11.603 7.963a.75.75 0 00-.977 1.138 2.5 2.5 0 01.142 3.667l-3 3a2.5 2.5 0 01-3.536-3.536l1.225-1.224a.75.75 0 00-1.061-1.06l-1.224 1.224a4 4 0 105.656 5.656l3-3a4 4 0 00-.225-5.865z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, text, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 243,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            plus: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"-ml-1.5 mr-1.5 h-5 w-5 border rounded-full   group-hover:\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                \"aria-hidden\": \"true\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z\",\n                    clipRule: \"evenodd\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 21\n                }, undefined)\n            }, text, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 256,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            archive: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"-ml-0.5 mr-1.5 h-5 w-5 group-hover:border-white\",\n                width: \"16\",\n                height: \"16\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 16 16\",\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M0 2a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1v7.5a2.5 2.5 0 0 1-2.5 2.5h-9A2.5 2.5 0 0 1 1 12.5V5a1 1 0 0 1-1-1zm2 3v7.5A1.5 1.5 0 0 0 3.5 14h9a1.5 1.5 0 0 0 1.5-1.5V5zm13-3H1v2h14zM5 7.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, text, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 272,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            new_vessel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"-ml-1 mr-1.5 w-10 h-10\",\n                viewBox: \"0 0 99 99\",\n                stroke: \"#022450\",\n                strokeWidth: \".7792px\",\n                strokeMiterlimit: \"10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        cx: \"49.5\",\n                        cy: \"49.5\",\n                        r: \"49.5\",\n                        fill: \"#f4fafe\",\n                        strokeWidth: \"0px\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        points: \"85.417 54.3973 81.2072 82.1557 25.4977 82.1557 21.8835 66.6894 85.417 54.3973\",\n                        fill: \"#ffffff\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        points: \"82.5 72.5 80.5 82.5 25.97 82.1557 24.5 76.5 82.5 72.5\",\n                        fill: \"#2a99ea\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M21.3172,66.1148c-.0401.113-.0469.2506-.0186.3727l3.7961,16.2454c.0372.1608.1301.2671.2328.2671h56.0744c.1144,0,.2142-.1311.2426-.3174l4.3477-28.6694c.0205-.1344,0-.2778-.0538-.3826-.0548-.1055-.1389-.1575-.2171-.1418l-5.7737,1.1171-3.7052-24.6484c-.0313-.2094-.1516-.3413-.2778-.3141l-33.9171,8.0939c-.0792.019-.1487.1006-.1858.2201-.0381.1195-.0391.2614-.0039.3834l1.8036,6.2314c.0469.1641.1516.2531.2602.2325l10.5959-2.5311,2.5641,16.7561-28.9656,5.6038-7.8786-48.3235c-.0362-.2226-.1692-.3562-.3081-.2952-.133.0618-.2113.2943-.1751.5194l.5493,3.3689-9.1919,10.3569c-.0871.0989-.1291.2778-.1047.4502s.1105.3001.2142.3207l11.256,2.204,5.135,31.4959-6.1091,1.1819c-.0773.0148-.1467.0899-.1858.202ZM11.9557,30.3244l8.4877-9.5634,1.8905,11.5951-10.3781-2.0317ZM79.4457,54.7029l-13.6483,2.6404-2.4007-17.0074,13.4396-2.9899,2.6094,17.3568ZM54.9468,41.6971c-.0323-.2086-.1506-.3389-.2778-.3125l-10.638,2.541-1.5728-5.4341,33.3537-7.9595.9005,5.9895-13.6561,3.0385c-.0704.0157-.134.0808-.1741.1797-.0401.0981-.0538.221-.0372.3372l2.4513,17.3632-7.7123,1.4921-2.6373-17.2352ZM85.417,54.3973l-4.2098,27.7584H25.4977l-3.6141-15.4663,63.5335-12.2921Z\",\n                        fill: \"#022450\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        points: \"11.9557 30.3244 20.4433 20.761 22.3338 32.3561 11.9557 30.3244\",\n                        fill: \"#2a99ea\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        points: \"79.4457 54.7029 65.7974 57.3433 63.3967 40.3359 76.8363 37.3461 79.4457 54.7029\",\n                        fill: \"#ffffff\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M54.9468,41.6971c-.0323-.2086-.1506-.3389-.2778-.3125l-10.638,2.541-1.5728-5.4341,33.3537-7.9595.9005,5.9895-13.6561,3.0385c-.0704.0157-.134.0808-.1741.1797-.0401.0981-.0538.221-.0372.3372l2.4513,17.3632-7.7123,1.4921-2.6373-17.2352Z\",\n                        fill: \"#ffffff\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, text, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 287,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            alert: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                viewBox: \"0 0 98.75 98.7516\",\n                stroke: \"#022450\",\n                strokeMiterlimit: \"10\",\n                strokeWidth: \".75px\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M49.375,1.1898c26.5687,0,48.1852,21.6165,48.1852,48.186s-21.6165,48.186-48.1852,48.186S1.1898,75.9453,1.1898,49.3758,22.8063,1.1898,49.375,1.1898Z\",\n                        fill: \"#ffffff\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M49.375,98.3766c27.0191,0,49-21.9817,49-49.0008S76.3941.375,49.375.375.375,22.3567.375,49.3758s21.9809,49.0008,49,49.0008ZM49.375,1.1898c26.5687,0,48.1852,21.6165,48.1852,48.186s-21.6165,48.186-48.1852,48.186S1.1898,75.9453,1.1898,49.3758,22.8063,1.1898,49.375,1.1898Z\",\n                        fill: \"#022450\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M40.1112,55.766h18.5277c.3237,0,.5877-.2875.5877-.6427V16.0185c0-.3552-.264-.6427-.5877-.6427h-18.5277c-.3237,0-.5877.2875-.5877.6427v39.1048c0,.3552.264.6427.5877.6427Z\",\n                        fill: \"#2a99ea\",\n                        strokeWidth: \"1.1315px\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M49.375,84.3758c5.82,0,10.5564-4.7352,10.5564-10.5564s-4.7364-10.5564-10.5564-10.5564-10.5564,4.7352-10.5564,10.5564,4.7364,10.5564,10.5564,10.5564Z\",\n                        fill: \"#2a99ea\",\n                        strokeWidth: \"1.1315px\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, text, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 330,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            location: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-7 h-7 sm:w-9 sm:h-9\",\n                viewBox: \"0 0 78 91.1323\",\n                stroke: \"#022450\",\n                fill: \"#ffffff\",\n                strokeMiterlimit: \"10\",\n                strokeWidth: \".75px\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M16.3623,67.2474c-.3242-.4395-.9844-.6133-1.438-.6133h-.8486c-7.2979,0-13.2354-5.9375-13.2354-13.2354V13.7357C.8403,6.4373,6.7778.4998,14.0757.4998h49.772c7.2979,0,13.2354,5.9375,13.2354,13.2358v39.6631c0,7.2979-5.9375,13.2354-13.2354,13.2354h-.8496c-.5312,0-1.041.2168-1.4336.6094l-22.5649,23.1729-22.6372-23.1689Z\",\n                        fill: \"#f4fafe\",\n                        strokeWidth: \"0px\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M63.8472,1c7.0223,0,12.7354,5.7131,12.7354,12.7354v39.663c0,7.0223-5.7131,12.7354-12.7354,12.7354h-.8488c-.6652,0-1.3.2684-1.7968.7654l-22.2029,22.8009-22.242-22.7644c-.4819-.5897-1.2558-.8019-1.8324-.8019h-.8488c-7.0223,0-12.7354-5.7131-12.7354-12.7354V13.7354C1.3401,6.7131,7.0532,1,14.0756,1h49.7717M63.8472,0H14.0756C6.5134,0,.3401,6.1732.3401,13.7354v39.663c0,7.5622,6.1732,13.7354,13.7354,13.7354h.8488c.3858,0,.8488.1543,1.0803.463l22.9953,23.5354,22.9181-23.5354c.3087-.3087.6945-.463,1.0803-.463h.8488c7.5622,0,13.7354-6.1732,13.7354-13.7354V13.7354c0-7.5622-6.1732-13.7354-13.7354-13.7354h0Z\",\n                        fill: \"#022450\",\n                        strokeWidth: \"0.5px\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M30.5667,71.4197c.0454.0732.126.1182.2124.1182h16.8809c.0864,0,.167-.0449.2124-.1182l19.064-30.7402c.0405-.0649.0488-.1445.0229-.2163-.0259-.0723-.0835-.1279-.1558-.1523l-7.9189-2.6618v-15.9271c0-.1382-.1118-.25-.25-.25h-7.0308v-13.0513c0-.1382-.1118-.25-.25-.25h-24.2686c-.1382,0-.25.1118-.25.25v13.0513h-7.0308c-.1382,0-.25.1118-.25.25v15.9271l-7.9189,2.6618c-.0723.0244-.1299.0801-.1558.1523-.0259.0718-.0176.1514.0229.2163l19.064,30.7402ZM66.345,40.6839l-18.8242,30.354h-8.0513V31.6502l26.8755,9.0336ZM27.3352,8.6707h23.7686v12.8013h-23.7686v-12.8013ZM20.0545,21.9719h38.3301v15.509l-19.0854-6.4152c-.0155-.0052-.0317.0021-.0477,0-.012-.0018-.0194-.0132-.0319-.0132s-.0199.0114-.0319.0132c-.016.0021-.0322-.0052-.0477,0l-19.0854,6.4152v-15.509ZM38.9695,31.6502v39.3876h-8.0513l-18.8242-30.354,26.8755-9.0336Z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        points: \"66.1255 40.6839 47.3013 71.0379 39.25 71.0379 39.25 31.6502 66.1255 40.6839\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        points: \"38.75 31.6502 38.75 71.0379 30.6987 71.0379 11.8745 40.6839 38.75 31.6502\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M19.835,21.9719h38.3301v15.509l-19.0854-6.4152c-.0155-.0052-.0317.0021-.0477,0-.012-.0018-.0194-.0132-.0319-.0132s-.0199.0114-.0319.0132c-.016.0021-.0322-.0052-.0477,0l-19.0854,6.4152v-15.509Z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M44.2812,14.8215h-3.0493c-.1382,0-.25.1118-.25.25s.1118.25.25.25h3.0493c.1382,0,.25-.1118.25-.25s-.1118-.25-.25-.25Z\",\n                        fill: \"#022450\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M36.7681,14.8215h-3.0493c-.1382,0-.25.1118-.25.25s.1118.25.25.25h3.0493c.1382,0,.25-.1118.25-.25s-.1118-.25-.25-.25Z\",\n                        fill: \"#022450\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, text, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 359,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            new_logbook: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"-ml-1.5 mr-1 h-5 w-5\",\n                viewBox: \"0 0 19 19\",\n                stroke: \"#022450\",\n                fill: \"#ffffff\",\n                strokeMiterlimit: \"10\",\n                strokeWidth: \".75px\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"\",\n                        d: \"M9,18a1.994,1.994,0,0,1-1.414-.586l-.828-.828A2,2,0,0,0,5.343,16H1a1,1,0,0,1-1-1V1A1,1,0,0,1,1,0H5A4.992,4.992,0,0,1,9,2Z\",\n                        transform: \"translate(0.5 0.5)\",\n                        fill: \"none\",\n                        strokeWidth: \"1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \" group-hover:stroke-white\",\n                        d: \"M0,18V2A4.992,4.992,0,0,1,4,0H8A1,1,0,0,1,9,1V15a1,1,0,0,1-1,1H3.657a2,2,0,0,0-1.414.586l-.828.828A1.994,1.994,0,0,1,0,18Z\",\n                        transform: \"translate(9.5 0.5)\",\n                        fill: \"none\",\n                        strokeWidth: \"1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, text, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 394,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            crew_cap: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"-ml-1 mr-1.5 w-10 h-10\",\n                viewBox: \"-4 -4 26 26.25\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        cx: \"9.5\",\n                        cy: \"9.125\",\n                        r: \"12\",\n                        fill: \"white\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M18.6935,7.7297h-2.3172c-.9285-2.994-3.7221-5.0658-6.8763-5.0658S3.5522,4.7357,2.6237,7.7297H.3065c-.0901,0-.1757.0396-.2341.1087s-.0832.16-.068.249l1.2258,7.2433c.0249.1473.1526.2554.3022.2554h15.9354c.1493,0,.277-.1079.3022-.2554l1.2258-7.2433c.0151-.0891-.0098-.18-.068-.249s-.144-.1087-.2341-.1087ZM.6175,8.3096h17.7656l-.356,1.8273H.9729l-.3554-1.8273ZM9.5,3.2768c2.8218,0,5.329,1.8115,6.233,4.4529H3.267c.904-2.6414,3.4112-4.4529,6.233-4.4529ZM17.2087,14.973H1.7914l-.7146-4.2233h16.8466l-.7146,4.2233Z\",\n                        fill: \"#022450\",\n                        stroke: \"#022450\",\n                        strokeMiterlimit: \"10\",\n                        strokeWidth: \"0px\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        cx: \"9.5\",\n                        cy: \"9.125\",\n                        r: \"12\",\n                        fill: \"none\",\n                        stroke: \"#022450\",\n                        strokeWidth: \"0.7792\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 434,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        points: \".6175 8.3096 18.3831 8.3096 18.0271 10.1368 .9729 10.1368 .6175 8.3096\",\n                        fill: \"#F2F4F7\",\n                        stroke: \"#022450\",\n                        strokeMiterlimit: \"10\",\n                        strokeWidth: \"0px\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, text, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 421,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            document_upload: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                viewBox: \"0 0 469.88 500.7\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        id: \"a\",\n                        \"data-name\": \"Layer 1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M102.05,455.38c-1.48,0-2.59-.37-3.7-1.11-1.85-1.11-2.96-2.59-3.33-4.44L.28,81.26c-1.11-4.07,1.11-8.14,5.18-9.25L283.74.22c1.85-.37,4.07-.37,5.55.74,1.85.74,2.96,2.59,3.33,4.44l19.24,74.38c.74,2.22,0,4.44-1.48,6.29s-3.7,2.96-5.92,2.96h-122.12v340.08c0,3.33-2.22,6.29-5.55,7.03l-72.9,18.87c-.74.37-1.11.37-1.85.37ZM16.2,84.59l91.03,354.51,60.32-15.54V81.63c0-4.07,3.33-7.4,7.4-7.4h119.9l-14.8-58.1L16.2,84.59Z\",\n                                    style: {\n                                        fill: \"#eb7c2a\",\n                                        strokeWidth: \"0px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M462.48,469.82H174.95c-4.07,0-7.4-3.33-7.4-7.4V81.63c0-4.07,3.33-7.4,7.4-7.4h196.13c1.85,0,3.7.74,5.18,2.22l91.4,91.77c1.48,1.48,2.22,3.33,2.22,5.18v289.01c0,4.07-3.33,7.4-7.4,7.4ZM182.35,455.01h272.73V176.37l-86.96-87.33h-185.77v365.98Z\",\n                                    style: {\n                                        fill: \"#eb7c2a\",\n                                        strokeWidth: \"0px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M16.2,84.59l91.03,354.51,60.32-15.54V81.63c0-4.07,3.33-7.4,7.4-7.4h119.9l-14.8-58.1L16.2,84.59Z\",\n                                    style: {\n                                        fill: \"#fff\",\n                                        strokeWidth: \"0px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                    points: \"182.35 455.01 455.08 455.01 455.08 176.37 368.11 89.03 182.35 89.03 182.35 455.01\",\n                                    style: {\n                                        fill: \"#fff\",\n                                        strokeWidth: \"0px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M462.48,177.11h-91.4c-2.22,0-3.7-1.48-3.7-3.7v-91.77c0-1.48.74-2.96,2.22-3.33,1.48-.74,2.96-.37,4.07.74l91.4,91.77c1.11,1.11,1.48,2.59.74,4.07-.37,1.11-1.85,2.22-3.33,2.22ZM374.78,169.7h78.82l-78.82-79.19v79.19Z\",\n                                    style: {\n                                        fill: \"#eb7c2a\",\n                                        strokeWidth: \"0px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M408.82,224.1h-180.21c-2.22,0-3.7-1.48-3.7-3.7s1.48-3.7,3.7-3.7h180.21c2.22,0,3.7,1.48,3.7,3.7s-1.48,3.7-3.7,3.7Z\",\n                                    style: {\n                                        fill: \"#2998e9\",\n                                        strokeWidth: \"0px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M408.82,286.64h-180.21c-2.22,0-3.7-1.48-3.7-3.7s1.48-3.7,3.7-3.7h180.21c2.22,0,3.7,1.48,3.7,3.7s-1.48,3.7-3.7,3.7Z\",\n                                    style: {\n                                        fill: \"#2998e9\",\n                                        strokeWidth: \"0px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M408.82,349.55h-180.21c-2.22,0-3.7-1.48-3.7-3.7s1.48-3.7,3.7-3.7h180.21c2.22,0,3.7,1.48,3.7,3.7,0,1.85-1.48,3.7-3.7,3.7Z\",\n                                    style: {\n                                        fill: \"#2998e9\",\n                                        strokeWidth: \"0px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M408.82,412.09h-180.21c-2.22,0-3.7-1.48-3.7-3.7s1.48-3.7,3.7-3.7h180.21c2.22,0,3.7,1.48,3.7,3.7s-1.48,3.7-3.7,3.7Z\",\n                                    style: {\n                                        fill: \"#2998e9\",\n                                        strokeWidth: \"0px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        id: \"b\",\n                        \"data-name\": \"Layer 2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M297.94,265.37h0c2.41,0,4.41,1.2,6.01,2.81l101.43,117.87c2,2.41,2.41,5.61,1.2,8.42s-4.01,4.81-7.22,4.81h-40.49s0,93.41,0,93.41c0,4.41-3.61,8.02-8.02,8.02h-105.84c-4.41,0-8.02-3.61-8.02-8.02v-93.01s-40.49,0-40.49,0c-3.21,0-6.01-2-7.22-4.81s-.8-6.01,1.2-8.42l101.43-117.87c1.6-2,3.61-3.21,6.01-3.21ZM381.73,383.24l-83.79-97.42-83.79,97.82h30.87c4.41,0,8.02,3.61,8.02,8.02v93.01s89.8,0,89.8,0v-93.01c0-4.41,3.61-8.02,8.02-8.02h30.87s0-.4,0-.4Z\",\n                                style: {\n                                    fill: \"#eb7c2a\",\n                                    strokeWidth: \"0px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M381.73,383.24l-83.79-97.42-83.79,97.82h30.87c4.41,0,8.02,3.61,8.02,8.02v93.01s89.8,0,89.8,0v-93.01c0-4.41,3.61-8.02,8.02-8.02h30.87s0-.4,0-.4Z\",\n                                style: {\n                                    fill: \"#fff\",\n                                    strokeWidth: \"0px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, text, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 454,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            inventory: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-5 w-10\",\n                viewBox: \"-8 -8 35 34.25\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        cx: \"9.5\",\n                        cy: \"9.125\",\n                        r: \"16\",\n                        fill: \"white\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        x: \".2681\",\n                        y: \"15.3634\",\n                        width: \"7.1756\",\n                        height: \"1.7875\",\n                        fill: \"#f2f4f7\",\n                        stroke: \"#022450\",\n                        strokeMiterlimit: \"10\",\n                        strokeWidth: \"0.25px\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        x: \"11.9577\",\n                        y: \"15.3634\",\n                        width: \"7.1756\",\n                        height: \"1.7875\",\n                        fill: \"#f2f4f7\",\n                        stroke: \"#022450\",\n                        strokeMiterlimit: \"10\",\n                        strokeWidth: \"0.25px\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M15.4984,9.1277l1.4943-7.5975c.0066-.0338-.0109-.0678-.0426-.0817L13.9446.125c-.0026-.0012-.0054-.0005-.0081-.0014-.0036-.0011-.0065-.0011-.0102-.0017-.016-.0026-.0319-.0009-.0462.007-.0009.0005-.0019-.0001-.0028.0004-.0001,0-.0021.0012-.0024.0014,0,0-.0001,0-.0002,0,0,0,0,0,0,0-.0412.0243-1.711.9955-4.1739.9955C7.24,1.1263,5.5679.155,5.5268.1308c0,0,0,0,0,0,0,0-.0001,0-.0002,0-.0003-.0002-.0023-.0013-.0024-.0014-.0009-.0006-.002,0-.0029-.0004-.0141-.0077-.0299-.0094-.0459-.0069-.0038.0006-.0069.0005-.0106.0017-.0026.0008-.0054.0002-.0079.0013l-3.0056,1.3236c-.0316.014-.0492.0479-.0426.0817l1.4943,7.5975L.1481,12.1682c-.0173.0141-.0273.0351-.0273.0573v7.8217c0,.0406.0331.0737.0737.0737h8.1825c.0196,0,.0383-.0078.0521-.0216l1.2716-1.2716,1.2716,1.2716c.0138.0138.0325.0216.0521.0216h8.1825c.0406,0,.0737-.0331.0737-.0737v-7.8217c0-.0222-.0101-.0432-.0273-.0573l-3.7548-3.0404ZM9.9007,15.1208l-.1263-7.6558c.4834-.1725,4.164-1.6693,4.2137-7.1599l2.8488,1.2545-1.4914,7.5832c-.0055.0268.0046.0544.0259.0715l3.6294,2.9065.1,3M9.7007,1.2736c2.1585,0,3.7208-.7367,4.1378-.9558-.0575,5.5134-3.8279,6.9121-4.1379,7.017-.3103-.1047-4.0803-1.4989-4.1378-7.017.417.2191,1.9793.9558,4.1378.9558ZM.3507,15.1208v-3l3.6794-2.9065c.0213-.0171.0314-.0447.0259-.0715L2.5645,1.5596,5.4134.3051c.0498,5.488,3.7275,6.9862,4.2137,7.16l.0737,7.6557h-2M.2681,15.3634h7.1756v1.7875H.2681v-1.7875ZM9.627,18.6929l-1.2806,1.2806H.2681v-2.6753h7.183c.0363.2613.2589.4638.53.4638h1.6459v.931ZM7.9811,17.6146c-.2152,0-.3901-.1749-.3901-.3901v-1.9348c0-.2152.1749-.3904.3901-.3904h1.7189s.0004.0003.0007.0003.0004-.0003.0007-.0003h1.7189c.2152,0,.3901.1752.3901.3904v1.9348c0,.2152-.1749.3901-.3901.3901h-3.4392ZM19.1333,19.9735h-8.0784l-1.2806-1.2806v-.931h1.6459c.2711,0,.4937-.2025.53-.4638h7.183v2.6753ZM19.1333,17.1509h-7.1756v-1.7875h7.1756v1.7875Z\",\n                        fill: \"#022450\",\n                        stroke: \"#022450\",\n                        strokeMiterlimit: \"10\",\n                        strokeWidth: \"0.25px\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M7.9811,17.6146c-.2152,0-.3901-.1749-.3901-.3901v-1.9348c0-.2152.1749-.3904.3901-.3904h1.7189s.0004.0003.0007.0003.0004-.0003.0007-.0003h1.7189c.2152,0,.3901.1752.3901.3904v1.9348c0,.2152-.1749.3901-.3901.3901h-3.4392Z\",\n                        fill: \"#f2f4f7\",\n                        stroke: \"#022450\",\n                        strokeMiterlimit: \"10\",\n                        strokeWidth: \"0.25px\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 546,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        cx: \"9.5\",\n                        cy: \"9.125\",\n                        r: \"16\",\n                        fill: \"none\",\n                        stroke: \"#022450\",\n                        strokeWidth: \"0.7792\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 553,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, text, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 513,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            cross_icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                viewBox: \"0 0 16 16\",\n                className: \"-ml-1.5 mr-2 h-7 w-7\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#f78f8f\",\n                        d: \"M7.5 0.5A7 7 0 1 0 7.5 14.5A7 7 0 1 0 7.5 0.5Z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 571,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#c74343\",\n                        d: \"M7.5,1C11.084,1,14,3.916,14,7.5S11.084,14,7.5,14S1,11.084,1,7.5S3.916,1,7.5,1 M7.5,0 C3.358,0,0,3.358,0,7.5S3.358,15,7.5,15S15,11.642,15,7.5S11.642,0,7.5,0L7.5,0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#fff\",\n                        d: \"M7 3.5H8V11.5H7z\",\n                        transform: \"rotate(45.001 7.5 7.5)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#fff\",\n                        d: \"M7 3.5H8V11.5H7z\",\n                        transform: \"rotate(134.999 7.5 7.5)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 584,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, text, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 566,\n                columnNumber: 17\n            }, undefined)\n        },\n        {\n            pin: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M12 13.5C13.933 13.5 15.5 11.933 15.5 10C15.5 8.067 13.933 6.5 12 6.5C10.067 6.5 8.5 8.067 8.5 10C8.5 11.933 10.067 13.5 12 13.5Z\",\n                        stroke: \"white\",\n                        strokeWidth: \"1.5\",\n                        fill: \"none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 599,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M12 22C16 18 20 14.4183 20 10C20 5.58172 16.4183 2 12 2C7.58172 2 4 5.58172 4 10C4 14.4183 8 18 12 22Z\",\n                        stroke: \"white\",\n                        strokeWidth: \"1.5\",\n                        fill: \"none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 605,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 594,\n                columnNumber: 17\n            }, undefined)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: link === undefined ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n            disabled: isDisabled === undefined ? false : isDisabled,\n            onClick: action,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    icon && icons.filter((item)=>item[icon]).map((item)=>item[icon]),\n                    counter > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-2 text-\".concat(counterColor ? counterColor : \"slred\", \"-800 border  rounded-full w-5 h-5 flex bg-\").concat(counterColor ? counterColor : \"slred\", \"-50 items-center justify-center border-\").concat(counterColor ? counterColor : \"slred\", \"-800\"),\n                        children: counter\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 29\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-1\",\n                        children: text\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 25\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 622,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n            lineNumber: 619,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            href: link,\n            className: \"\".concat(type === \"text\" ? \"inline-flex justify-center items-center\" : \"block\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                disabled: isDisabled === undefined ? false : isDisabled,\n                onClick: action,\n                children: [\n                    icon && icons.filter((item)=>item[icon]).map((item)=>item[icon]),\n                    counter > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-2 text-\".concat(counterColor ? counterColor : \"slred\", \"-800 border  rounded-full w-5 h-5 flex bg-\").concat(counterColor ? counterColor : \"slred\", \"-50 items-center justify-center border-\").concat(counterColor ? counterColor : \"slred\", \"-800\"),\n                        children: counter\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 29\n                    }, undefined),\n                    text\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n                lineNumber: 640,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\sea-logs-button.tsx\",\n            lineNumber: 637,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_c = SeaLogsButton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SeaLogsButton);\nvar _c;\n$RefreshReg$(_c, \"SeaLogsButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/sea-logs-button.tsx\n"));

/***/ })

});