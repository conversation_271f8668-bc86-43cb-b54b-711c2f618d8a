"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7";
exports.ids = ["vendor-chunks/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/BaseCollection.mjs":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/BaseCollection.mjs ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseCollection: () => (/* binding */ $23b9f4fcf0fe224b$export$408d25a4e12db025),\n/* harmony export */   CollectionNode: () => (/* binding */ $23b9f4fcf0fe224b$export$d68d59712b04d9d1)\n/* harmony export */ });\n/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ class $23b9f4fcf0fe224b$export$d68d59712b04d9d1 {\n    get childNodes() {\n        throw new Error('childNodes is not supported');\n    }\n    clone() {\n        let node = new $23b9f4fcf0fe224b$export$d68d59712b04d9d1(this.type, this.key);\n        node.value = this.value;\n        node.level = this.level;\n        node.hasChildNodes = this.hasChildNodes;\n        node.rendered = this.rendered;\n        node.textValue = this.textValue;\n        node['aria-label'] = this['aria-label'];\n        node.index = this.index;\n        node.parentKey = this.parentKey;\n        node.prevKey = this.prevKey;\n        node.nextKey = this.nextKey;\n        node.firstChildKey = this.firstChildKey;\n        node.lastChildKey = this.lastChildKey;\n        node.props = this.props;\n        node.render = this.render;\n        return node;\n    }\n    constructor(type, key){\n        this.value = null;\n        this.level = 0;\n        this.hasChildNodes = false;\n        this.rendered = null;\n        this.textValue = '';\n        this['aria-label'] = undefined;\n        this.index = 0;\n        this.parentKey = null;\n        this.prevKey = null;\n        this.nextKey = null;\n        this.firstChildKey = null;\n        this.lastChildKey = null;\n        this.props = {};\n        this.type = type;\n        this.key = key;\n    }\n}\nclass $23b9f4fcf0fe224b$export$408d25a4e12db025 {\n    get size() {\n        return this.keyMap.size;\n    }\n    getKeys() {\n        return this.keyMap.keys();\n    }\n    *[Symbol.iterator]() {\n        let node = this.firstKey != null ? this.keyMap.get(this.firstKey) : undefined;\n        while(node){\n            yield node;\n            node = node.nextKey != null ? this.keyMap.get(node.nextKey) : undefined;\n        }\n    }\n    getChildren(key) {\n        let keyMap = this.keyMap;\n        return {\n            *[Symbol.iterator] () {\n                let parent = keyMap.get(key);\n                let node = (parent === null || parent === void 0 ? void 0 : parent.firstChildKey) != null ? keyMap.get(parent.firstChildKey) : null;\n                while(node){\n                    yield node;\n                    node = node.nextKey != null ? keyMap.get(node.nextKey) : undefined;\n                }\n            }\n        };\n    }\n    getKeyBefore(key) {\n        let node = this.keyMap.get(key);\n        if (!node) return null;\n        if (node.prevKey != null) {\n            node = this.keyMap.get(node.prevKey);\n            while(node && node.type !== 'item' && node.lastChildKey != null)node = this.keyMap.get(node.lastChildKey);\n            var _node_key;\n            return (_node_key = node === null || node === void 0 ? void 0 : node.key) !== null && _node_key !== void 0 ? _node_key : null;\n        }\n        return node.parentKey;\n    }\n    getKeyAfter(key) {\n        let node = this.keyMap.get(key);\n        if (!node) return null;\n        if (node.type !== 'item' && node.firstChildKey != null) return node.firstChildKey;\n        while(node){\n            if (node.nextKey != null) return node.nextKey;\n            if (node.parentKey != null) node = this.keyMap.get(node.parentKey);\n            else return null;\n        }\n        return null;\n    }\n    getFirstKey() {\n        return this.firstKey;\n    }\n    getLastKey() {\n        let node = this.lastKey != null ? this.keyMap.get(this.lastKey) : null;\n        while((node === null || node === void 0 ? void 0 : node.lastChildKey) != null)node = this.keyMap.get(node.lastChildKey);\n        var _node_key;\n        return (_node_key = node === null || node === void 0 ? void 0 : node.key) !== null && _node_key !== void 0 ? _node_key : null;\n    }\n    getItem(key) {\n        var _this_keyMap_get;\n        return (_this_keyMap_get = this.keyMap.get(key)) !== null && _this_keyMap_get !== void 0 ? _this_keyMap_get : null;\n    }\n    at() {\n        throw new Error('Not implemented');\n    }\n    clone() {\n        // We need to clone using this.constructor so that subclasses have the right prototype.\n        // TypeScript isn't happy about this yet.\n        // https://github.com/microsoft/TypeScript/issues/3841\n        let Constructor = this.constructor;\n        let collection = new Constructor();\n        collection.keyMap = new Map(this.keyMap);\n        collection.firstKey = this.firstKey;\n        collection.lastKey = this.lastKey;\n        return collection;\n    }\n    addNode(node) {\n        if (this.frozen) throw new Error('Cannot add a node to a frozen collection');\n        this.keyMap.set(node.key, node);\n    }\n    removeNode(key) {\n        if (this.frozen) throw new Error('Cannot remove a node to a frozen collection');\n        this.keyMap.delete(key);\n    }\n    commit(firstKey, lastKey, isSSR = false) {\n        if (this.frozen) throw new Error('Cannot commit a frozen collection');\n        this.firstKey = firstKey;\n        this.lastKey = lastKey;\n        this.frozen = !isSSR;\n    }\n    // TODO: this is pretty specific to menu, will need to check if it is generic enough\n    // Will need to handle varying levels I assume but will revisit after I get searchable menu working for base menu\n    // TODO: an alternative is to simply walk the collection and add all item nodes that match the filter and any sections/separators we encounter\n    // to an array, then walk that new array and fix all the next/Prev keys while adding them to the new collection\n    filter(filterFn) {\n        let newCollection = new $23b9f4fcf0fe224b$export$408d25a4e12db025();\n        // This tracks the absolute last node we've visited in the collection when filtering, used for setting up the filteredCollection's lastKey and\n        // for updating the next/prevKey for every non-filtered node.\n        let lastNode = null;\n        for (let node of this){\n            if (node.type === 'section' && node.hasChildNodes) {\n                let clonedSection = node.clone();\n                let lastChildInSection = null;\n                for (let child of this.getChildren(node.key))if (filterFn(child.textValue) || child.type === 'header') {\n                    let clonedChild = child.clone();\n                    // eslint-disable-next-line max-depth\n                    if (lastChildInSection == null) clonedSection.firstChildKey = clonedChild.key;\n                    // eslint-disable-next-line max-depth\n                    if (newCollection.firstKey == null) newCollection.firstKey = clonedSection.key;\n                    // eslint-disable-next-line max-depth\n                    if (lastChildInSection && lastChildInSection.parentKey === clonedChild.parentKey) {\n                        lastChildInSection.nextKey = clonedChild.key;\n                        clonedChild.prevKey = lastChildInSection.key;\n                    } else clonedChild.prevKey = null;\n                    clonedChild.nextKey = null;\n                    newCollection.addNode(clonedChild);\n                    lastChildInSection = clonedChild;\n                }\n                // Add newly filtered section to collection if it has any valid child nodes, otherwise remove it and its header if any\n                if (lastChildInSection) {\n                    if (lastChildInSection.type !== 'header') {\n                        clonedSection.lastChildKey = lastChildInSection.key;\n                        // If the old prev section was filtered out, will need to attach to whatever came before\n                        // eslint-disable-next-line max-depth\n                        if (lastNode == null) clonedSection.prevKey = null;\n                        else if (lastNode.type === 'section' || lastNode.type === 'separator') {\n                            lastNode.nextKey = clonedSection.key;\n                            clonedSection.prevKey = lastNode.key;\n                        }\n                        clonedSection.nextKey = null;\n                        lastNode = clonedSection;\n                        newCollection.addNode(clonedSection);\n                    } else {\n                        if (newCollection.firstKey === clonedSection.key) newCollection.firstKey = null;\n                        newCollection.removeNode(lastChildInSection.key);\n                    }\n                }\n            } else if (node.type === 'separator') {\n                // will need to check if previous section key exists, if it does then we add the separator to the collection.\n                // After the full collection is created we'll need to remove it it is the last node in the section (aka no following section after the separator)\n                let clonedSeparator = node.clone();\n                clonedSeparator.nextKey = null;\n                if ((lastNode === null || lastNode === void 0 ? void 0 : lastNode.type) === 'section') {\n                    lastNode.nextKey = clonedSeparator.key;\n                    clonedSeparator.prevKey = lastNode.key;\n                    lastNode = clonedSeparator;\n                    newCollection.addNode(clonedSeparator);\n                }\n            } else if (filterFn(node.textValue)) {\n                let clonedNode = node.clone();\n                if (newCollection.firstKey == null) newCollection.firstKey = clonedNode.key;\n                if (lastNode != null && lastNode.type !== 'section' && lastNode.type !== 'separator' && lastNode.parentKey === clonedNode.parentKey) {\n                    lastNode.nextKey = clonedNode.key;\n                    clonedNode.prevKey = lastNode.key;\n                } else clonedNode.prevKey = null;\n                clonedNode.nextKey = null;\n                newCollection.addNode(clonedNode);\n                lastNode = clonedNode;\n            }\n        }\n        if ((lastNode === null || lastNode === void 0 ? void 0 : lastNode.type) === 'separator' && lastNode.nextKey === null) {\n            let lastSection;\n            if (lastNode.prevKey != null) {\n                lastSection = newCollection.getItem(lastNode.prevKey);\n                lastSection.nextKey = null;\n            }\n            newCollection.removeNode(lastNode.key);\n            lastNode = lastSection;\n        }\n        newCollection.lastKey = (lastNode === null || lastNode === void 0 ? void 0 : lastNode.key) || null;\n        return newCollection;\n    }\n    constructor(){\n        this.keyMap = new Map();\n        this.firstKey = null;\n        this.lastKey = null;\n        this.frozen = false;\n    }\n}\n\n\n\n//# sourceMappingURL=BaseCollection.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/BaseCollection.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/CollectionBuilder.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/CollectionBuilder.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Collection: () => (/* binding */ $e1995378a142960e$export$fb8073518f34e6ec),\n/* harmony export */   CollectionBuilder: () => (/* binding */ $e1995378a142960e$export$bf788dd355e3a401),\n/* harmony export */   createBranchComponent: () => (/* binding */ $e1995378a142960e$export$e953bb1cd0f19726),\n/* harmony export */   createLeafComponent: () => (/* binding */ $e1995378a142960e$export$18af5c7a9e9b3664)\n/* harmony export */ });\n/* harmony import */ var _BaseCollection_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./BaseCollection.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/BaseCollection.mjs\");\n/* harmony import */ var _Document_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Document.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/Document.mjs\");\n/* harmony import */ var _useCachedChildren_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./useCachedChildren.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/useCachedChildren.mjs\");\n/* harmony import */ var _Hidden_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Hidden.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/Hidden.mjs\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_aria_ssr__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-aria/ssr */ \"(ssr)/./node_modules/.pnpm/@react-aria+ssr@3.9.7_react@18.3.1/node_modules/@react-aria/ssr/dist/SSRProvider.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.27.0_re_4bf881c7f169b3927cbbceda9562f024/node_modules/@react-aria/utils/dist/useLayoutEffect.mjs\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/.pnpm/use-sync-external-store@1.4.0_react@18.3.1/node_modules/use-sync-external-store/shim/index.js\");\n\n\n\n\n\n\n\n\n\n\n/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n\n\n\n\n\n\nconst $e1995378a142960e$var$ShallowRenderContext = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__.createContext)(false);\nconst $e1995378a142960e$var$CollectionDocumentContext = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction $e1995378a142960e$export$bf788dd355e3a401(props) {\n    // If a document was provided above us, we're already in a hidden tree. Just render the content.\n    let doc = (0, react__WEBPACK_IMPORTED_MODULE_1__.useContext)($e1995378a142960e$var$CollectionDocumentContext);\n    if (doc) // The React types prior to 18 did not allow returning ReactNode from components\n    // even though the actual implementation since React 16 did.\n    // We must return ReactElement so that TS does not complain that <CollectionBuilder>\n    // is not a valid JSX element with React 16 and 17 types.\n    // https://github.com/DefinitelyTyped/DefinitelyTyped/issues/20544\n    return props.content;\n    // Otherwise, render a hidden copy of the children so that we can build the collection before constructing the state.\n    // This should always come before the real DOM content so we have built the collection by the time it renders during SSR.\n    // This is fine. CollectionDocumentContext never changes after mounting.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    let { collection: collection, document: document } = $e1995378a142960e$var$useCollectionDocument(props.createCollection);\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement((0, react__WEBPACK_IMPORTED_MODULE_1__).Fragment, null, /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement((0, _Hidden_mjs__WEBPACK_IMPORTED_MODULE_3__.Hidden), null, /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement($e1995378a142960e$var$CollectionDocumentContext.Provider, {\n        value: document\n    }, props.content)), /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement($e1995378a142960e$var$CollectionInner, {\n        render: props.children,\n        collection: collection\n    }));\n}\nfunction $e1995378a142960e$var$CollectionInner({ collection: collection, render: render }) {\n    return render(collection);\n}\n// React 16 and 17 don't support useSyncExternalStore natively, and the shim provided by React does not support getServerSnapshot.\n// This wrapper uses the shim, but additionally calls getServerSnapshot during SSR (according to SSRProvider).\nfunction $e1995378a142960e$var$useSyncExternalStoreFallback(subscribe, getSnapshot, getServerSnapshot) {\n    let isSSR = (0, _react_aria_ssr__WEBPACK_IMPORTED_MODULE_4__.useIsSSR)();\n    let isSSRRef = (0, react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isSSR);\n    // This is read immediately inside the wrapper, which also runs during render.\n    // We just need a ref to avoid invalidating the callback itself, which\n    // would cause React to re-run the callback more than necessary.\n    // eslint-disable-next-line rulesdir/pure-render\n    isSSRRef.current = isSSR;\n    let getSnapshotWrapper = (0, react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return isSSRRef.current ? getServerSnapshot() : getSnapshot();\n    }, [\n        getSnapshot,\n        getServerSnapshot\n    ]);\n    return (0, use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_2__.useSyncExternalStore)(subscribe, getSnapshotWrapper);\n}\nconst $e1995378a142960e$var$useSyncExternalStore = typeof (0, react__WEBPACK_IMPORTED_MODULE_1__)['useSyncExternalStore'] === 'function' ? (0, react__WEBPACK_IMPORTED_MODULE_1__)['useSyncExternalStore'] : $e1995378a142960e$var$useSyncExternalStoreFallback;\nfunction $e1995378a142960e$var$useCollectionDocument(createCollection) {\n    // The document instance is mutable, and should never change between renders.\n    // useSyncExternalStore is used to subscribe to updates, which vends immutable Collection objects.\n    let [document] = (0, react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new (0, _Document_mjs__WEBPACK_IMPORTED_MODULE_5__.Document)((createCollection === null || createCollection === void 0 ? void 0 : createCollection()) || new (0, _BaseCollection_mjs__WEBPACK_IMPORTED_MODULE_6__.BaseCollection)()));\n    let subscribe = (0, react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((fn)=>document.subscribe(fn), [\n        document\n    ]);\n    let getSnapshot = (0, react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        let collection = document.getCollection();\n        if (document.isSSR) // After SSR is complete, reset the document to empty so it is ready for React to render the portal into.\n        // We do this _after_ getting the collection above so that the collection still has content in it from SSR\n        // during the current render, before React has finished the client render.\n        document.resetAfterSSR();\n        return collection;\n    }, [\n        document\n    ]);\n    let getServerSnapshot = (0, react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        document.isSSR = true;\n        return document.getCollection();\n    }, [\n        document\n    ]);\n    let collection = $e1995378a142960e$var$useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_7__.useLayoutEffect)(()=>{\n        document.isMounted = true;\n        return ()=>{\n            // Mark unmounted so we can skip all of the collection updates caused by \n            // React calling removeChild on every item in the collection.\n            document.isMounted = false;\n        };\n    }, [\n        document\n    ]);\n    return {\n        collection: collection,\n        document: document\n    };\n}\nconst $e1995378a142960e$var$SSRContext = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction $e1995378a142960e$var$useSSRCollectionNode(Type, props, ref, rendered, children, render) {\n    // During SSR, portals are not supported, so the collection children will be wrapped in an SSRContext.\n    // Since SSR occurs only once, we assume that the elements are rendered in order and never re-render.\n    // Therefore we can create elements in our collection document during render so that they are in the\n    // collection by the time we need to use the collection to render to the real DOM.\n    // After hydration, we switch to client rendering using the portal.\n    let itemRef = (0, react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((element)=>{\n        element === null || element === void 0 ? void 0 : element.setProps(props, ref, rendered, render);\n    }, [\n        props,\n        ref,\n        rendered,\n        render\n    ]);\n    let parentNode = (0, react__WEBPACK_IMPORTED_MODULE_1__.useContext)($e1995378a142960e$var$SSRContext);\n    if (parentNode) {\n        // Guard against double rendering in strict mode.\n        let element = parentNode.ownerDocument.nodesByProps.get(props);\n        if (!element) {\n            element = parentNode.ownerDocument.createElement(Type);\n            element.setProps(props, ref, rendered, render);\n            parentNode.appendChild(element);\n            parentNode.ownerDocument.updateCollection();\n            parentNode.ownerDocument.nodesByProps.set(props, element);\n        }\n        return children ? /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement($e1995378a142960e$var$SSRContext.Provider, {\n            value: element\n        }, children) : null;\n    }\n    // @ts-ignore\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement(Type, {\n        ref: itemRef\n    }, children);\n}\nfunction $e1995378a142960e$export$18af5c7a9e9b3664(type, render) {\n    let Component = ({ node: node })=>render(node.props, node.props.ref, node);\n    let Result = (0, react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, ref)=>{\n        let isShallow = (0, react__WEBPACK_IMPORTED_MODULE_1__.useContext)($e1995378a142960e$var$ShallowRenderContext);\n        if (!isShallow) {\n            if (render.length >= 3) throw new Error(render.name + ' cannot be rendered outside a collection.');\n            return render(props, ref);\n        }\n        return $e1995378a142960e$var$useSSRCollectionNode(type, props, ref, 'children' in props ? props.children : null, null, (node)=>/*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement(Component, {\n                node: node\n            }));\n    });\n    // @ts-ignore\n    Result.displayName = render.name;\n    return Result;\n}\nfunction $e1995378a142960e$export$e953bb1cd0f19726(type, render, useChildren = $e1995378a142960e$var$useCollectionChildren) {\n    let Component = ({ node: node })=>render(node.props, node.props.ref, node);\n    let Result = (0, react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, ref)=>{\n        let children = useChildren(props);\n        var _useSSRCollectionNode;\n        return (_useSSRCollectionNode = $e1995378a142960e$var$useSSRCollectionNode(type, props, ref, null, children, (node)=>/*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement(Component, {\n                node: node\n            }))) !== null && _useSSRCollectionNode !== void 0 ? _useSSRCollectionNode : /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement((0, react__WEBPACK_IMPORTED_MODULE_1__).Fragment, null);\n    });\n    // @ts-ignore\n    Result.displayName = render.name;\n    return Result;\n}\nfunction $e1995378a142960e$var$useCollectionChildren(options) {\n    return (0, _useCachedChildren_mjs__WEBPACK_IMPORTED_MODULE_8__.useCachedChildren)({\n        ...options,\n        addIdAndValue: true\n    });\n}\nconst $e1995378a142960e$var$CollectionContext = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction $e1995378a142960e$export$fb8073518f34e6ec(props) {\n    let ctx = (0, react__WEBPACK_IMPORTED_MODULE_1__.useContext)($e1995378a142960e$var$CollectionContext);\n    let dependencies = ((ctx === null || ctx === void 0 ? void 0 : ctx.dependencies) || []).concat(props.dependencies);\n    let idScope = props.idScope || (ctx === null || ctx === void 0 ? void 0 : ctx.idScope);\n    let children = $e1995378a142960e$var$useCollectionChildren({\n        ...props,\n        idScope: idScope,\n        dependencies: dependencies\n    });\n    let doc = (0, react__WEBPACK_IMPORTED_MODULE_1__.useContext)($e1995378a142960e$var$CollectionDocumentContext);\n    if (doc) children = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement($e1995378a142960e$var$CollectionRoot, null, children);\n    // Propagate dependencies and idScope to child collections.\n    ctx = (0, react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            dependencies: dependencies,\n            idScope: idScope\n        }), [\n        idScope,\n        ...dependencies\n    ]);\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement($e1995378a142960e$var$CollectionContext.Provider, {\n        value: ctx\n    }, children);\n}\nfunction $e1995378a142960e$var$CollectionRoot({ children: children }) {\n    let doc = (0, react__WEBPACK_IMPORTED_MODULE_1__.useContext)($e1995378a142960e$var$CollectionDocumentContext);\n    let wrappedChildren = (0, react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>/*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement($e1995378a142960e$var$CollectionDocumentContext.Provider, {\n            value: null\n        }, /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement($e1995378a142960e$var$ShallowRenderContext.Provider, {\n            value: true\n        }, children)), [\n        children\n    ]);\n    // During SSR, we render the content directly, and append nodes to the document during render.\n    // The collection children return null so that nothing is actually rendered into the HTML.\n    return (0, _react_aria_ssr__WEBPACK_IMPORTED_MODULE_4__.useIsSSR)() ? /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement($e1995378a142960e$var$SSRContext.Provider, {\n        value: doc\n    }, wrappedChildren) : /*#__PURE__*/ (0, react_dom__WEBPACK_IMPORTED_MODULE_0__.createPortal)(wrappedChildren, doc);\n}\n\n\n\n//# sourceMappingURL=CollectionBuilder.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/CollectionBuilder.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/Document.mjs":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/Document.mjs ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseNode: () => (/* binding */ $681cc3c98f569e39$export$410b0c854570d131),\n/* harmony export */   Document: () => (/* binding */ $681cc3c98f569e39$export$b34a105447964f9f),\n/* harmony export */   ElementNode: () => (/* binding */ $681cc3c98f569e39$export$dc064fe9e59310fd)\n/* harmony export */ });\n/* harmony import */ var _BaseCollection_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BaseCollection.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/BaseCollection.mjs\");\n\n\n/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \nclass $681cc3c98f569e39$export$410b0c854570d131 {\n    *[Symbol.iterator]() {\n        let node = this.firstChild;\n        while(node){\n            yield node;\n            node = node.nextSibling;\n        }\n    }\n    get firstChild() {\n        return this._firstChild;\n    }\n    set firstChild(firstChild) {\n        this._firstChild = firstChild;\n        this.ownerDocument.markDirty(this);\n    }\n    get lastChild() {\n        return this._lastChild;\n    }\n    set lastChild(lastChild) {\n        this._lastChild = lastChild;\n        this.ownerDocument.markDirty(this);\n    }\n    get previousSibling() {\n        return this._previousSibling;\n    }\n    set previousSibling(previousSibling) {\n        this._previousSibling = previousSibling;\n        this.ownerDocument.markDirty(this);\n    }\n    get nextSibling() {\n        return this._nextSibling;\n    }\n    set nextSibling(nextSibling) {\n        this._nextSibling = nextSibling;\n        this.ownerDocument.markDirty(this);\n    }\n    get parentNode() {\n        return this._parentNode;\n    }\n    set parentNode(parentNode) {\n        this._parentNode = parentNode;\n        this.ownerDocument.markDirty(this);\n    }\n    get isConnected() {\n        var _this_parentNode;\n        return ((_this_parentNode = this.parentNode) === null || _this_parentNode === void 0 ? void 0 : _this_parentNode.isConnected) || false;\n    }\n    appendChild(child) {\n        this.ownerDocument.startTransaction();\n        if (child.parentNode) child.parentNode.removeChild(child);\n        if (this.firstChild == null) this.firstChild = child;\n        if (this.lastChild) {\n            this.lastChild.nextSibling = child;\n            child.index = this.lastChild.index + 1;\n            child.previousSibling = this.lastChild;\n        } else {\n            child.previousSibling = null;\n            child.index = 0;\n        }\n        child.parentNode = this;\n        child.nextSibling = null;\n        this.lastChild = child;\n        this.ownerDocument.markDirty(this);\n        if (child.hasSetProps) // Only add the node to the collection if we already received props for it.\n        // Otherwise wait until then so we have the correct id for the node.\n        this.ownerDocument.addNode(child);\n        this.ownerDocument.endTransaction();\n        this.ownerDocument.queueUpdate();\n    }\n    insertBefore(newNode, referenceNode) {\n        if (referenceNode == null) return this.appendChild(newNode);\n        this.ownerDocument.startTransaction();\n        if (newNode.parentNode) newNode.parentNode.removeChild(newNode);\n        newNode.nextSibling = referenceNode;\n        newNode.previousSibling = referenceNode.previousSibling;\n        newNode.index = referenceNode.index;\n        if (this.firstChild === referenceNode) this.firstChild = newNode;\n        else if (referenceNode.previousSibling) referenceNode.previousSibling.nextSibling = newNode;\n        referenceNode.previousSibling = newNode;\n        newNode.parentNode = referenceNode.parentNode;\n        let node = referenceNode;\n        while(node){\n            node.index++;\n            node = node.nextSibling;\n        }\n        if (newNode.hasSetProps) this.ownerDocument.addNode(newNode);\n        this.ownerDocument.endTransaction();\n        this.ownerDocument.queueUpdate();\n    }\n    removeChild(child) {\n        if (child.parentNode !== this || !this.ownerDocument.isMounted) return;\n        this.ownerDocument.startTransaction();\n        let node = child.nextSibling;\n        while(node){\n            node.index--;\n            node = node.nextSibling;\n        }\n        if (child.nextSibling) child.nextSibling.previousSibling = child.previousSibling;\n        if (child.previousSibling) child.previousSibling.nextSibling = child.nextSibling;\n        if (this.firstChild === child) this.firstChild = child.nextSibling;\n        if (this.lastChild === child) this.lastChild = child.previousSibling;\n        child.parentNode = null;\n        child.nextSibling = null;\n        child.previousSibling = null;\n        child.index = 0;\n        this.ownerDocument.removeNode(child);\n        this.ownerDocument.endTransaction();\n        this.ownerDocument.queueUpdate();\n    }\n    addEventListener() {}\n    removeEventListener() {}\n    constructor(ownerDocument){\n        this._firstChild = null;\n        this._lastChild = null;\n        this._previousSibling = null;\n        this._nextSibling = null;\n        this._parentNode = null;\n        this.ownerDocument = ownerDocument;\n    }\n}\nclass $681cc3c98f569e39$export$dc064fe9e59310fd extends $681cc3c98f569e39$export$410b0c854570d131 {\n    get index() {\n        return this._index;\n    }\n    set index(index) {\n        this._index = index;\n        this.ownerDocument.markDirty(this);\n    }\n    get level() {\n        if (this.parentNode instanceof $681cc3c98f569e39$export$dc064fe9e59310fd) return this.parentNode.level + (this.node.type === 'item' ? 1 : 0);\n        return 0;\n    }\n    updateNode() {\n        var _this_previousSibling, _this_nextSibling, _this_firstChild, _this_lastChild;\n        let node = this.ownerDocument.getMutableNode(this);\n        node.index = this.index;\n        node.level = this.level;\n        node.parentKey = this.parentNode instanceof $681cc3c98f569e39$export$dc064fe9e59310fd ? this.parentNode.node.key : null;\n        var _this_previousSibling_node_key;\n        node.prevKey = (_this_previousSibling_node_key = (_this_previousSibling = this.previousSibling) === null || _this_previousSibling === void 0 ? void 0 : _this_previousSibling.node.key) !== null && _this_previousSibling_node_key !== void 0 ? _this_previousSibling_node_key : null;\n        var _this_nextSibling_node_key;\n        node.nextKey = (_this_nextSibling_node_key = (_this_nextSibling = this.nextSibling) === null || _this_nextSibling === void 0 ? void 0 : _this_nextSibling.node.key) !== null && _this_nextSibling_node_key !== void 0 ? _this_nextSibling_node_key : null;\n        node.hasChildNodes = !!this.firstChild;\n        var _this_firstChild_node_key;\n        node.firstChildKey = (_this_firstChild_node_key = (_this_firstChild = this.firstChild) === null || _this_firstChild === void 0 ? void 0 : _this_firstChild.node.key) !== null && _this_firstChild_node_key !== void 0 ? _this_firstChild_node_key : null;\n        var _this_lastChild_node_key;\n        node.lastChildKey = (_this_lastChild_node_key = (_this_lastChild = this.lastChild) === null || _this_lastChild === void 0 ? void 0 : _this_lastChild.node.key) !== null && _this_lastChild_node_key !== void 0 ? _this_lastChild_node_key : null;\n    }\n    setProps(obj, ref, rendered, render) {\n        let node = this.ownerDocument.getMutableNode(this);\n        let { value: value, textValue: textValue, id: id, ...props } = obj;\n        props.ref = ref;\n        node.props = props;\n        node.rendered = rendered;\n        node.render = render;\n        node.value = value;\n        node.textValue = textValue || (typeof props.children === 'string' ? props.children : '') || obj['aria-label'] || '';\n        if (id != null && id !== node.key) {\n            if (this.hasSetProps) throw new Error('Cannot change the id of an item');\n            node.key = id;\n        }\n        // If this is the first time props have been set, end the transaction started in the constructor\n        // so this node can be emitted.\n        if (!this.hasSetProps) {\n            this.ownerDocument.addNode(this);\n            this.ownerDocument.endTransaction();\n            this.hasSetProps = true;\n        }\n        this.ownerDocument.queueUpdate();\n    }\n    get style() {\n        return {};\n    }\n    hasAttribute() {}\n    setAttribute() {}\n    setAttributeNS() {}\n    removeAttribute() {}\n    constructor(type, ownerDocument){\n        super(ownerDocument), this.nodeType = 8 // COMMENT_NODE (we'd use ELEMENT_NODE but React DevTools will fail to get its dimensions)\n        , this._index = 0, this.hasSetProps = false;\n        this.node = new (0, _BaseCollection_mjs__WEBPACK_IMPORTED_MODULE_0__.CollectionNode)(type, `react-aria-${++ownerDocument.nodeId}`);\n        // Start a transaction so that no updates are emitted from the collection\n        // until the props for this node are set. We don't know the real id for the\n        // node until then, so we need to avoid emitting collections in an inconsistent state.\n        this.ownerDocument.startTransaction();\n    }\n}\nclass $681cc3c98f569e39$export$b34a105447964f9f extends $681cc3c98f569e39$export$410b0c854570d131 {\n    get isConnected() {\n        return this.isMounted;\n    }\n    createElement(type) {\n        return new $681cc3c98f569e39$export$dc064fe9e59310fd(type, this);\n    }\n    /**\n   * Lazily gets a mutable instance of a Node. If the node has already\n   * been cloned during this update cycle, it just returns the existing one.\n   */ getMutableNode(element) {\n        let node = element.node;\n        if (!this.mutatedNodes.has(element)) {\n            node = element.node.clone();\n            this.mutatedNodes.add(element);\n            element.node = node;\n        }\n        this.markDirty(element);\n        return node;\n    }\n    getMutableCollection() {\n        if (!this.isSSR && !this.collectionMutated) {\n            this.collection = this.collection.clone();\n            this.collectionMutated = true;\n        }\n        return this.collection;\n    }\n    markDirty(node) {\n        this.dirtyNodes.add(node);\n    }\n    startTransaction() {\n        this.transactionCount++;\n    }\n    endTransaction() {\n        this.transactionCount--;\n    }\n    addNode(element) {\n        let collection = this.getMutableCollection();\n        if (!collection.getItem(element.node.key)) {\n            collection.addNode(element.node);\n            for (let child of element)this.addNode(child);\n        }\n        this.markDirty(element);\n    }\n    removeNode(node) {\n        for (let child of node)this.removeNode(child);\n        let collection = this.getMutableCollection();\n        collection.removeNode(node.node.key);\n        this.markDirty(node);\n    }\n    /** Finalizes the collection update, updating all nodes and freezing the collection. */ getCollection() {\n        if (this.transactionCount > 0) return this.collection;\n        this.updateCollection();\n        return this.collection;\n    }\n    updateCollection() {\n        for (let element of this.dirtyNodes)if (element instanceof $681cc3c98f569e39$export$dc064fe9e59310fd && element.isConnected) element.updateNode();\n        this.dirtyNodes.clear();\n        if (this.mutatedNodes.size || this.collectionMutated) {\n            var _this_firstChild, _this_lastChild;\n            let collection = this.getMutableCollection();\n            for (let element of this.mutatedNodes)if (element.isConnected) collection.addNode(element.node);\n            var _this_firstChild_node_key, _this_lastChild_node_key;\n            collection.commit((_this_firstChild_node_key = (_this_firstChild = this.firstChild) === null || _this_firstChild === void 0 ? void 0 : _this_firstChild.node.key) !== null && _this_firstChild_node_key !== void 0 ? _this_firstChild_node_key : null, (_this_lastChild_node_key = (_this_lastChild = this.lastChild) === null || _this_lastChild === void 0 ? void 0 : _this_lastChild.node.key) !== null && _this_lastChild_node_key !== void 0 ? _this_lastChild_node_key : null, this.isSSR);\n            this.mutatedNodes.clear();\n        }\n        this.collectionMutated = false;\n    }\n    queueUpdate() {\n        // Don't emit any updates if there is a transaction in progress.\n        // queueUpdate should be called again after the transaction.\n        if (this.dirtyNodes.size === 0 || this.transactionCount > 0) return;\n        for (let fn of this.subscriptions)fn();\n    }\n    subscribe(fn) {\n        this.subscriptions.add(fn);\n        return ()=>this.subscriptions.delete(fn);\n    }\n    resetAfterSSR() {\n        if (this.isSSR) {\n            this.isSSR = false;\n            this.firstChild = null;\n            this.lastChild = null;\n            this.nodeId = 0;\n        }\n    }\n    constructor(collection){\n        // @ts-ignore\n        super(null), this.nodeType = 11 // DOCUMENT_FRAGMENT_NODE\n        , this.ownerDocument = this, this.dirtyNodes = new Set(), this.isSSR = false, this.nodeId = 0, this.nodesByProps = new WeakMap(), this.isMounted = true, this.mutatedNodes = new Set(), this.subscriptions = new Set(), this.transactionCount = 0;\n        this.collection = collection;\n        this.collectionMutated = true;\n    }\n}\n\n\n\n//# sourceMappingURL=Document.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/Document.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/Hidden.mjs":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/Hidden.mjs ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hidden: () => (/* binding */ $f39a9eba43920ace$export$8dc98ba7eadeaa56),\n/* harmony export */   HiddenContext: () => (/* binding */ $f39a9eba43920ace$export$94b6d0abf7d33e8c),\n/* harmony export */   createHideableComponent: () => (/* binding */ $f39a9eba43920ace$export$86427a43e3e48ebb),\n/* harmony export */   useIsHidden: () => (/* binding */ $f39a9eba43920ace$export$b5d7cc18bb8d2b59)\n/* harmony export */ });\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_aria_ssr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/ssr */ \"(ssr)/./node_modules/.pnpm/@react-aria+ssr@3.9.7_react@18.3.1/node_modules/@react-aria/ssr/dist/SSRProvider.mjs\");\n\n\n\n\n/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n// React doesn't understand the <template> element, which doesn't have children like a normal element.\n// It will throw an error during hydration when it expects the firstChild to contain content rendered\n// on the server, when in reality, the browser will have placed this inside the `content` document fragment.\n// This monkey patches the firstChild property for our special hidden template elements to work around this error.\n// See https://github.com/facebook/react/issues/19932\nif (typeof HTMLTemplateElement !== 'undefined') {\n    const getFirstChild = Object.getOwnPropertyDescriptor(Node.prototype, 'firstChild').get;\n    Object.defineProperty(HTMLTemplateElement.prototype, 'firstChild', {\n        configurable: true,\n        enumerable: true,\n        get: function() {\n            if (this.dataset.reactAriaHidden) return this.content.firstChild;\n            else return getFirstChild.call(this);\n        }\n    });\n}\nconst $f39a9eba43920ace$export$94b6d0abf7d33e8c = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__.createContext)(false);\n// Portal to nowhere\nconst $f39a9eba43920ace$var$hiddenFragment = typeof DocumentFragment !== 'undefined' ? new DocumentFragment() : null;\nfunction $f39a9eba43920ace$export$8dc98ba7eadeaa56(props) {\n    let isHidden = (0, react__WEBPACK_IMPORTED_MODULE_1__.useContext)($f39a9eba43920ace$export$94b6d0abf7d33e8c);\n    let isSSR = (0, _react_aria_ssr__WEBPACK_IMPORTED_MODULE_2__.useIsSSR)();\n    if (isHidden) // Don't hide again if we are already hidden.\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement((0, react__WEBPACK_IMPORTED_MODULE_1__).Fragment, null, props.children);\n    let children = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement($f39a9eba43920ace$export$94b6d0abf7d33e8c.Provider, {\n        value: true\n    }, props.children);\n    // In SSR, portals are not supported by React. Instead, render into a <template>\n    // element, which the browser will never display to the user. In addition, the\n    // content is not part of the DOM tree, so it won't affect ids or other accessibility attributes.\n    return isSSR ? /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_1__).createElement(\"template\", {\n        \"data-react-aria-hidden\": true\n    }, children) : /*#__PURE__*/ (0, react_dom__WEBPACK_IMPORTED_MODULE_0__.createPortal)(children, $f39a9eba43920ace$var$hiddenFragment);\n}\nfunction $f39a9eba43920ace$export$86427a43e3e48ebb(fn) {\n    let Wrapper = (props, ref)=>{\n        let isHidden = (0, react__WEBPACK_IMPORTED_MODULE_1__.useContext)($f39a9eba43920ace$export$94b6d0abf7d33e8c);\n        if (isHidden) return null;\n        return fn(props, ref);\n    };\n    // @ts-ignore - for react dev tools\n    Wrapper.displayName = fn.displayName || fn.name;\n    return (0, react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(Wrapper);\n}\nfunction $f39a9eba43920ace$export$b5d7cc18bb8d2b59() {\n    return (0, react__WEBPACK_IMPORTED_MODULE_1__.useContext)($f39a9eba43920ace$export$94b6d0abf7d33e8c);\n}\n\n\n\n//# sourceMappingURL=Hidden.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/Hidden.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/useCachedChildren.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/useCachedChildren.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCachedChildren: () => (/* binding */ $e948873055cbafe4$export$727c8fc270210f13)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \nfunction $e948873055cbafe4$export$727c8fc270210f13(props) {\n    let { children: children, items: items, idScope: idScope, addIdAndValue: addIdAndValue, dependencies: dependencies = [] } = props;\n    // Invalidate the cache whenever the parent value changes.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    let cache = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>new WeakMap(), dependencies);\n    return (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (items && typeof children === 'function') {\n            let res = [];\n            for (let item of items){\n                let rendered = cache.get(item);\n                if (!rendered) {\n                    rendered = children(item);\n                    var _rendered_props_id, _ref;\n                    // @ts-ignore\n                    let key = (_ref = (_rendered_props_id = rendered.props.id) !== null && _rendered_props_id !== void 0 ? _rendered_props_id : item.key) !== null && _ref !== void 0 ? _ref : item.id;\n                    if (key == null) throw new Error('Could not determine key for item');\n                    if (idScope) key = idScope + ':' + key;\n                    // Note: only works if wrapped Item passes through id...\n                    rendered = (0, react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(rendered, addIdAndValue ? {\n                        key: key,\n                        id: key,\n                        value: item\n                    } : {\n                        key: key\n                    });\n                    cache.set(item, rendered);\n                }\n                res.push(rendered);\n            }\n            return res;\n        } else if (typeof children !== 'function') return children;\n    }, [\n        children,\n        items,\n        cache,\n        idScope,\n        addIdAndValue\n    ]);\n}\n\n\n\n//# sourceMappingURL=useCachedChildren.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+collections@3.0_aed2afda974bef32ea713863d11f8ba7/node_modules/@react-aria/collections/dist/useCachedChildren.mjs\n");

/***/ })

};
;