'use client'
import React, { useState } from 'react'
import dayjs from 'dayjs'
import { Button } from '@/components/ui/button'

import { TimePicker } from '@/components/ui/time-picker'
import { Clock } from 'lucide-react'

export default function RadioTimeField({
    log,
    handleTimeChange,
    fieldName = 'Time',
    buttonLabel = 'Set To Now',
    hideButton = false,
}: {
    log: any
    handleTimeChange: any
    fieldName?: any
    buttonLabel?: any
    hideButton?: any
}) {
    const [time, setTime] = useState<any>(log.time ? dayjs(log.time) : false)
    const handleTimeFieldChange = (time: any) => {
        setTime(time)
        handleTimeChange(log, time)
    }

    return (
        <div className="flex flex-row gap-2 items-center">
            <TimePicker
                value={time ? dayjs(time).toDate() : new Date()}
                onChange={(newTime) => {
                    handleTimeFieldChange(newTime)
                }}
                use24Hour={true}
                className="w-[120px]"
            />
            {!hideButton && (
                <div className="flex flex-wrap flex-row">
                    <Button
                        onClick={() => {
                            handleTimeFieldChange(dayjs().toDate())
                        }}
                        className="text-nowrap">
                        {buttonLabel}
                    </Button>
                </div>
            )}
        </div>
    )
}
